using System;
using System.Collections;
using Common;
using UnityEngine;

public enum E_PlayerFacingDirection
{
    Left,
    Right
}
public enum E_PlayerMovingState
{
    Walk = 4,
    Run = 11
}

/// <summary>
/// 玩家移动组件
/// </summary>
public class Q_PlayerMoveComp : MonoBehaviour
{
    static Vector3 PlayerLeftSide = new(0.2f, 0.2f, 1);
    static Vector3 PlayerRightSide = new(-0.2f, 0.2f, 1);
    public int MoveSpeed = 8;

    public E_PlayerMovingState PlayerMovingState = E_PlayerMovingState.Walk;
    private Vector3 moveInput;
    private Animator m_animator;

    private Vector2 playerLimit = new();
    private Vector2 cameraLimit = new();
    private Transform camAnchor;
    private Rigidbody2D m_rigidBody;
    public Vector3 CamOffset = new(0, 1.6f, -10);
    private E_PlayerFacingDirection _facingDirection;
    public E_PlayerFacingDirection PlayerFacingDirection
    {
        get
        {
            return _facingDirection;
        }
        set
        {
            if (_facingDirection == value)
            {
                return;
            }
            _facingDirection = value;
            transform.localScale = value == E_PlayerFacingDirection.Left ? PlayerLeftSide : PlayerRightSide;

        }
    }

    private bool _isMoving;
    public bool IsMoving
    {
        get
        {
            return _isMoving;
        }
    }
    private Vector3 tmp = new();

    public void SetPlayerMoveLimit(Vector2 vec)
    {
        float halfHeight = Camera.main.orthographicSize;
        float halfWidth = halfHeight * Camera.main.aspect;
        playerLimit = vec;
        cameraLimit.Set(
            vec.x + halfWidth,
             vec.y - halfWidth
        );
        Debug.Log($"limit:{vec} cameraLimit:{cameraLimit}");
    }

    void Awake()
    {
        camAnchor = transform.Find("MainCameraAnchor");
        m_animator = GetComponent<Animator>();
        m_rigidBody = GetComponent<Rigidbody2D>();
        m_rigidBody.bodyType = RigidbodyType2D.Dynamic;
        PlayerFacingDirection = E_PlayerFacingDirection.Left;
    }

    void Update()
    {
        moveInput.x = Input.GetAxisRaw("Horizontal");
        moveInput.y = 0;
        moveInput.z = 0;
    }

    void FixedUpdate()
    {
        if (moveInput.x != 0)
        {
            PlayerFacingDirection = moveInput.x < 0 ? E_PlayerFacingDirection.Left : E_PlayerFacingDirection.Right;
        }

        if (Math.Abs(moveInput.x) > 0.1)
        {
            if (!_isMoving)
            {
                _isMoving = true;
                m_rigidBody.constraints = RigidbodyConstraints2D.FreezeRotation;
                // m_animator.Play("walk");
                // if (PlayerMovingState == E_PlayerMovingState.Run)
                // {
                //     m_animator.Play("run");
                // }
                // else
                // {
                //     m_animator.Play("walk");
                // }
                m_animator.SetInteger("Speed", (int)PlayerMovingState);
            }

        }
        else
        {
            if (_isMoving)
            {
                _isMoving = false;
                ClearFreezing();
                StartCoroutine(FreezingLazier());
                m_animator.SetInteger("Speed", 0);
                // m_animator.Play(Const.DefaultSpineAnimName);
            }
        }


        Vector3 targetPos = transform.position + moveInput * MoveSpeed * Time.fixedDeltaTime;
        ClampPos(playerLimit, ref targetPos);
        transform.position = Vector3.Lerp(transform.position, targetPos, 0.9f);

        ForceSetCamPos();

    }

    public void ForceSetCamPos()
    {
        tmp = camAnchor.position;
        ClampPos(cameraLimit, ref tmp);
        CamOffset.y = -transform.position.y;
        Camera.main.transform.position = tmp + CamOffset;
    }

    Coroutine _freezingLazier;
    void ClearFreezing()
    {
        if (_freezingLazier != null)
        {
            StopCoroutine(_freezingLazier);
            _freezingLazier = null;
        }
    }
    IEnumerator FreezingLazier()
    {
        yield return new WaitForSeconds(0.2f);
        if (!IsMoving)
        {
            m_rigidBody.constraints = RigidbodyConstraints2D.FreezeAll;
        }
    }


    void ClampPos(Vector2 limit, ref Vector3 targetPos)
    {

        if (targetPos.x < limit.x)
        {
            targetPos.x = limit.x;
        }
        if (targetPos.x > limit.y)
        {
            targetPos.x = limit.y;
        }
    }

}
