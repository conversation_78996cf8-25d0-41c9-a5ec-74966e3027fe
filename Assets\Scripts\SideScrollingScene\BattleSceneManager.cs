
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using EventObjSystem;
using UnityEngine;
using static EventsTable;

namespace SideScrollingScene
{

    public class BattleSceneManager : ChunkStreamingManager
    {
        private static BattleSceneManager _instance;

        public static BattleSceneManager Instance
        {
            get
            {
                return _instance;
            }
        }

        protected override void Awake()
        {
            base.Awake();
            InputManager.Instance.InitConfig();
            _instance = this;
        }

        public Q_PlayerComp Q_Player
        {
            get
            {
                return player;
            }
        }

        /// <summary>
        /// 解决一个事件，重新生成npc的时间
        /// </summary>
        [Header("解决一个事件,重新生成npc的时间")]
        public float EventObjCoolDown = 2f;

        [Header("战斗场景id")]
        public int BattleSceneId = -1;

        [Header("场景默认事件对象类型 - 生成场景数据时会自动把默认场景数据洗成默认类型")]
        public EventObjType SceneEventObjType = EventObjType.Npc;


        private SceneEventEntry _sceneEventEntry;
        private EventInfo _curLeftInfo;
        private EventInfo _curRightInfo;
        private int _totalEventsCount = 0;
        private int _eventCnt = 0;
        private float _cd = 0;

        List<EventInfo> _eventInfos = new();
        List<EventInfo> _fixedEventInfos = new();

        void OnDestroy()
        {
            if (_curLeftInfo != null)
            {
                _curLeftInfo.DestoryEvent();
                _curLeftInfo = null;
                _cd = 0;
            }

            if (_curRightInfo != null)
            {
                _curRightInfo.DestoryEvent();
                _curRightInfo = null;
                _cd = 0;
            }

            _eventInfos.Clear();
            _eventInfos = null;

            if (_fixedEventInfos != null)
            {
                foreach (EventInfo info in _fixedEventInfos)
                {
                    info.DestoryEvent();
                }
            }
            _fixedEventInfos.Clear();
            _fixedEventInfos = null;
        }

        protected override void OnAfterPlayerInitBeforePlayerActive()
        {
            base.OnAfterPlayerInitBeforePlayerActive();
            InitBattleScene();
        }

        public void InitBattleScene()
        {
            InitRandomEvent();
            UpdateNearestLeftPoint();
            UpdateNearestRightPoint();
            InitFixedEvent();
        }

        void InitRandomEvent()
        {
            _sceneEventEntry = ExcelDataMgr.Instance.GetTable<SceneEventInfo>().GetSceneEventEntry(BattleSceneId);
            _eventCnt = 0;
            foreach (int num in _sceneEventEntry.ratio)
            {
                _totalEventsCount += num;
            }
            foreach (Chunk chunk in SceneChunks)
            {
                if (chunk.RandomEventPoints.Count > 0)
                {
                    foreach (EventSpawnPoint point in chunk.RandomEventPoints)
                    {
                        _eventInfos.Add(new(point));
                    }
                }
            }
        }

        int totalCnt;
        void InitFixedEvent()
        {
            foreach (Chunk chunk in SceneChunks)
            {
                if (chunk.FixedEventPoints.Count > 0)
                {
                    foreach (EventSpawnPoint point in chunk.FixedEventPoints)
                    {
                        EventInfo info = new(point);
                        _fixedEventInfos.Add(info);
                    }
                }
            }
            totalCnt = _fixedEventInfos.Count;
            SpawnFixedEvents();
        }

        void SpawnFixedEvents()
        {
            UniTask.Delay(100).ContinueWith(() =>
                       {
                           if (totalCnt >= 1)
                           {
                               _fixedEventInfos[totalCnt - 1].InitEvent(_fixedEventInfos[totalCnt - 1].EventId);
                               totalCnt--;
                               SpawnFixedEvents();
                           }
                       });
        }

        public void DestroyEventObj(IEventObj eventObj)
        {
            if (_curLeftInfo != null && _curLeftInfo.EventObj == eventObj)
            {
                _curLeftInfo.DestoryEvent();
                _curLeftInfo = null;
                _cd = 0;
            }

            if (_curRightInfo != null && _curRightInfo.EventObj == eventObj)
            {
                _curRightInfo.DestoryEvent();
                _curRightInfo = null;
                _cd = 0;
            }
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();
            UpdateEventObj();

        }

        /// <summary>
        /// 刷新事件npc
        /// </summary>
        void UpdateEventObj()
        {
            if (_eventCnt >= _totalEventsCount)
            {
                return;
            }

            if (_eventCnt >= _eventInfos.Count)
            {
                return;
            }

            if (_cd < EventObjCoolDown)
            {
                _cd += Time.deltaTime;
                return;
            }
            _cd = 0;

            SwitchCurInfo();

            if (_curLeftInfo == null && UpdateNearestLeftPoint())
            {
                return;
            }

            if (_curRightInfo == null && UpdateNearestRightPoint())
            {
                return;
            }

        }

        void SwitchCurInfo()
        {
            Vector3 pos = player.gameObject.transform.position;
            if (_curLeftInfo != null)
            {
                bool isLeft = _curLeftInfo.IsByPlayerLeft(pos);
                bool isRightLeft = _curRightInfo != null && _curRightInfo.IsByPlayerLeft(pos);

                //右边的在左边 左边的在右边
                if (!isLeft && isRightLeft)
                {
                    SwitchLeftAndRight();
                }

                //都在左边
                if (isLeft && isRightLeft && _curRightInfo.Distance(pos) > _curLeftInfo.Distance(pos))
                {
                    SwitchLeftAndRight();
                }

                //都在右边
                if (!isLeft && !isRightLeft && (_curRightInfo == null || _curRightInfo.Distance(pos) < _curLeftInfo.Distance(pos)))
                {
                    SwitchLeftAndRight();
                }

            }
            else if (_curRightInfo != null && _curRightInfo.IsByPlayerLeft(pos))
            {
                SwitchLeftAndRight();
            }
        }
        void SwitchLeftAndRight()
        {
            var tmp = _curRightInfo;
            _curRightInfo = _curLeftInfo;
            _curLeftInfo = tmp;
        }

        int GetEventId()
        {
            int limit_1 = _sceneEventEntry.ratio[0];
            int limit_2 = _sceneEventEntry.ratio[1];
            int eventId = -1;
            if (_eventCnt >= 0 && _eventCnt <= limit_1)
            {
                int randIdx = Random.Range(0, _sceneEventEntry.eventIds_1.Length);
                eventId = _sceneEventEntry.eventIds_1[randIdx];
            }
            else if (_eventCnt <= limit_1 + limit_2)
            {
                int randIdx = Random.Range(0, _sceneEventEntry.eventIds_2.Length);
                eventId = _sceneEventEntry.eventIds_2[randIdx];
            }
            else if (_eventCnt <= _totalEventsCount)
            {
                int randIdx = Random.Range(0, _sceneEventEntry.eventIds_3.Length);
                eventId = _sceneEventEntry.eventIds_3[randIdx];
            }
            Debug.Log($"EventId:{eventId}");
            return eventId;
        }

        bool UpdateNearestLeftPoint()
        {
            float minLeft = float.PositiveInfinity;

            foreach (EventInfo info in _eventInfos)
            {
                Vector3 pos = player.gameObject.transform.position;
                if (info.EventState == EventStat.Upspawn && info.IsByPlayerLeft(pos) && minLeft > info.Distance(pos))
                {
                    minLeft = info.Distance(pos);
                    _curLeftInfo = info;
                }
            }

            if (_curLeftInfo != null)
            {
                int id = GetEventId();
                if (id != -1)
                {
                    _curLeftInfo.InitEvent(id);
                    _eventCnt++;
                    return true;
                }
                else
                {
                    _curLeftInfo = null;
                }
            }
            return false;
        }

        bool UpdateNearestRightPoint()
        {

            float minRight = float.PositiveInfinity;
            foreach (EventInfo info in _eventInfos)
            {
                Vector3 pos = player.gameObject.transform.position;
                if (info.EventState == EventStat.Upspawn && !info.IsByPlayerLeft(pos) && minRight > info.Distance(pos))
                {
                    minRight = info.Distance(pos);
                    _curRightInfo = info;
                }
            }

            if (_curRightInfo != null)
            {
                int id = GetEventId();
                if (id != -1)
                {
                    _curRightInfo.InitEvent(id);
                    _eventCnt++;
                    return true;
                }
                else
                {
                    _curRightInfo = null;
                }
            }
            return false;
        }
    }
}