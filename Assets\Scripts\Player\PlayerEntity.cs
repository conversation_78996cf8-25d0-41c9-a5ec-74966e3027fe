﻿using Common;
using Cysharp.Threading.Tasks;
using UnityEngine;


namespace Player
{
    public class PlayerEntity : UnityCommunity.UnitySingleton.Singleton<PlayerEntity>
    {
        private string _userName = "财柒";
        public string UserName => _userName;

        private Q_PlayerComp _qPlayer;
        public Q_PlayerComp Q_Player
        {
            get
            {
                return _qPlayer;
            }
        }

        public int _gameDays = 0;
        public int GameDays => _gameDays;

        public void NextDay()
        {
            _gameDays += 1;
            EventManager.Instance.TriggerEvent(EventName.OnDayChanged, this);
        }

        protected override void OnInitialized()
        {
            //初始化 玩家姓名
            ExcelDataMgr.Instance.SetTranslationFunc<NpcDatas, NpcData>("name");
            NpcDatas npcDatas = ExcelDataMgr.Instance.GetTable<NpcDatas>();
            _userName = npcDatas.GetNpcData(0).name;
        }



        public async UniTask<GameObject> InitQPlayer()
        {
            GameObject qPlayer = await ResourceLoader.Instance.LoadAsync<GameObject>(ResourcePathConst.Q_Player);
            Q_PlayerComp characterBase = qPlayer.GetComponent<Q_PlayerComp>();
            characterBase.Initialize(0);
            _qPlayer = characterBase;
            return qPlayer;
        }

        public void ReturnQPlayer(GameObject qPlayer)
        {
            ResourceLoader.Instance.ReleaseInstance(qPlayer);
        }
    }
}