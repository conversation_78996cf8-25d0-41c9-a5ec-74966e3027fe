﻿{
    "configs": {
        "field_row": 0,
        "type_row": 1,
        "data_from_row": 3
    },
    "excels": [
        {
            "excel_name": "Excels/DialogDatas.xlsx",
            "script_directory": "Assets/Scripts/Datas",
            "asset_directory": "Assets/Resources/datas",
            "name_space": "",
            "use_hash_string": false,
            "hide_asset_properties": false,
            "use_public_items_getter": false,
            "compress_color_into_int": true,
            "treat_unknown_types_as_enum": false,
            "generate_tostring_method": true,
            "slaves": [
                {
                    "excel_name": "Excels/NpcDatas.xlsx",
                    "asset_directory": "Assets/Resources/datas"
                }
            ]
        },
        {
            "excel_name": "Excels/NpcDatas.xlsx",
            "script_directory": "Assets/Scripts/Datas",
            "asset_directory": "Assets/Resources/datas",
            "name_space": "",
            "use_hash_string": false,
            "hide_asset_properties": false,
            "use_public_items_getter": false,
            "compress_color_into_int": true,
            "treat_unknown_types_as_enum": false,
            "generate_tostring_method": true,
            "slaves": []
        },
        {
            "excel_name": "Excels/OptionTranslation.xlsx",
            "script_directory": "Assets/Scripts/Datas",
            "asset_directory": "Assets/Resources/datas",
            "name_space": "",
            "use_hash_string": false,
            "hide_asset_properties": false,
            "use_public_items_getter": false,
            "compress_color_into_int": true,
            "treat_unknown_types_as_enum": false,
            "generate_tostring_method": true,
            "slaves": []
        },
        {
            "excel_name": "Excels/Attributes.xlsx",
            "script_directory": "Assets/Scripts/Datas",
            "asset_directory": "Assets/Resources/datas",
            "name_space": "",
            "use_hash_string": false,
            "hide_asset_properties": false,
            "use_public_items_getter": false,
            "compress_color_into_int": true,
            "treat_unknown_types_as_enum": false,
            "generate_tostring_method": true,
            "slaves": []
        },
        {
            "excel_name": "Excels/SceneEventInfo.xlsx",
            "script_directory": "Assets/Scripts/Datas",
            "asset_directory": "Assets/Resources/datas",
            "name_space": "",
            "use_hash_string": false,
            "hide_asset_properties": false,
            "use_public_items_getter": false,
            "compress_color_into_int": true,
            "treat_unknown_types_as_enum": false,
            "generate_tostring_method": true,
            "slaves": []
        },
        {
            "excel_name": "Excels/EventsTable.xlsx",
            "script_directory": "Assets/Scripts/Datas",
            "asset_directory": "Assets/Resources/datas",
            "name_space": "",
            "use_hash_string": false,
            "hide_asset_properties": false,
            "use_public_items_getter": false,
            "compress_color_into_int": true,
            "treat_unknown_types_as_enum": true,
            "generate_tostring_method": true,
            "slaves": []
        }
    ]
}