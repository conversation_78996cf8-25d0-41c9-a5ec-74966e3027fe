using AttributeSystem;
using Common;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace CharacterSystem
{
    /// <summary>
    /// 一个角色实体
    /// </summary>
    public class CharacterEntity : MonoBehaviour, IAttributeHolder
    {
        public int CharacterID = -1;
        public int GetAttributeUniqueID()
        {
            return CharacterID;
        }

        public AttributeComp GetAttributeComp()
        {
            return gameObject.GetComponent<AttributeComp>();
        }

        private NpcData _npcData;
        public NpcData NpcData
        {
            get
            {
                return _npcData;
            }
        }

        private GameObject _qnpc;
        public GameObject Qnpc
        {
            get
            {
                return _qnpc;
            }
        }

        public async UniTask<GameObject> GetQNpc(int characterId)
        {
            GameObject qNpc = await ResourceLoader.Instance.LoadAsync<GameObject>(ResourcePathConst.Q_Player);
            CharacterBase characterBase = qNpc.GetComponent<CharacterBase>();
            characterBase.Initialize(characterId);
            _qnpc = qNpc;
            return qNpc;
        }

        public void DestroyQNpc(GameObject qNpc)
        {
            ResourceLoader.Instance.ReleaseInstance(qNpc);
            _qnpc = null;
        }

    }
}