using System;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityCommunity.UnitySingleton;
using UnityEngine;


public enum E_UILayer
{
    CachePool,
    Bottom,
    Middle,
    Top,
    System,
    Penetration
}

public enum E_UIState
{
    //局内展示
    InSession,
    //局外展示
    OutOfSession,
    //局内外均可展示 - 默认
    InOrOutOfSession
}


/// <summary>
/// UI管理器
/// 1.管理所有显示的面板
/// 2.提供给外部 显示和隐藏等等接口
/// </summary>
public class UIService : UnityCommunity.UnitySingleton.Singleton<UIService>
{

    /// <summary>
    /// UI预制体路径
    /// </summary>
    private readonly string PrefabPath = "Assets/Resources/UIPrefabs";

    /// <summary>
    /// 存储ui加载路径
    /// </summary>
    private Dictionary<string, string> _uiLoadPathDic = new();

    private Dictionary<string, string> _uiNameDic = new();

    /// <summary>
    /// 通过show/hide 直接操控的 单独开一个防止引用出错
    /// </summary>
    private Dictionary<string, YuriUIBehaviour> _uiDic = new();

    /// <summary>
    /// ui可见性查询
    /// </summary>
    private Dictionary<YuriUIBehaviour, bool> _uiVisibilityDic = new();

    // 缓存池+数据存储双管理 - 防止内存泄漏
    // 通过showUI/hideUI/createUI/DestroyUI 操控的ui实例字典
    private Dictionary<string, List<YuriUIBehaviour>> _uiCreateDic = new();
    private Dictionary<string, List<YuriUIBehaviour>> _uiPoolDic = new();
    private Dictionary<E_UILayer, Transform> _layerDic = new();

    /// <summary>
    /// 正在显示的ui
    /// </summary>
    private List<YuriUIBehaviour> _activeUIs = new();

    private GameObject _eventSystem;
    private Transform _rootCanvas;

    #region 初始化相关
    protected override void OnInitialized()
    {
        InitUI();
        InitUIPath();
    }

    void InitUI()
    {
        GameObject obj = Resources.Load<GameObject>("UI/UIRootCanvas");
        obj.name = "UIRootCanvas";

        _rootCanvas = obj.transform;
        GameObject.DontDestroyOnLoad(obj);

        //找到各个层级
        _layerDic.Add(E_UILayer.Bottom, _rootCanvas.Find("Bottom"));
        _layerDic.Add(E_UILayer.Middle, _rootCanvas.Find("Middle"));
        _layerDic.Add(E_UILayer.Top, _rootCanvas.Find("Top"));
        _layerDic.Add(E_UILayer.System, _rootCanvas.Find("System"));
        _layerDic.Add(E_UILayer.CachePool, _rootCanvas.Find("CachePool"));
        _layerDic.Add(E_UILayer.Penetration, _rootCanvas.Find("Penetration"));

        // 创建EventSystem 让其过场景的时候 不被移除
        // _eventSystem = ResourceMgr.Ins().Load<GameObject>("UI/EventSystem");
        // GameObject.DontDestroyOnLoad(_eventSystem);
    }

    void InitUIPath()
    {
        foreach (string prefabName in UIDefine.UIPrefabNames)
        {
            if (prefabName == "")
            {
                continue;
            }
            string clsName = $"{prefabName}Generated";
            if (!_uiLoadPathDic.ContainsKey(clsName))
            {
                string path = $"UIPrefabs/{prefabName}";
                _uiLoadPathDic.Add(clsName, path);
                _uiNameDic.Add(path, prefabName);
                // Debug.Log($"____addUILoadPath___clsName1111:{clsName} path:{path}");

            }
        }

        // if (Directory.Exists(PrefabPath))
        // {
        //     // 获取文件夹下的所有文件
        //     string[] files = Directory.GetFiles(PrefabPath);
        //     foreach (string filePath in files)
        //     {
        //         string path = filePath[17..];
        //         path = path.Replace('\\', '/');
        //         path = path.Split('.')[0];
        //         string[] pathSplits = path.Split('/');
        //         string prefabName = pathSplits[pathSplits.Length - 1];
        //         string clsName = $"{prefabName}Generated";
        //         if (!_uiLoadPathDic.ContainsKey(clsName))
        //         {
        //             _uiLoadPathDic.Add(clsName, path);
        //             _uiNameDic.Add(path, prefabName);
        //         }
        //         Debug.Log($"____addUILoadPath___clsName222:{clsName} path:{path}");
        //     }
        // }
        // else
        // {
        //     Debug.LogError($"Folder does not exist: {PrefabPath}");
        // }
    }
    #endregion

    #region UI单例的显示,隐藏与销毁

    public void Show<T>(params object[] values) where T : YuriUIBehaviour
    {
        T sc = GetUIScrpitAndInit<T>();
        sc.OnShow(values);
        AddUIToActiveUIs(sc);
    }

    T GetUIScrpitAndInit<T>() where T : YuriUIBehaviour
    {
        Type type = typeof(T);
        T sc;
        string typeName = type.ToString();
        if (_uiDic.ContainsKey(typeName))
        {
            sc = _uiDic[typeName] as T;
        }
        else
        {
            sc = CreateUI<T>();
            _uiDic.Add(typeName, sc);
        }
        AddToLayer(sc.transform, sc.Layer);
        sc.gameObject.SetActive(true);
        _uiVisibilityDic[sc] = true;
        return sc;
    }

    public void Hide<T>() where T : YuriUIBehaviour
    {
        Type type = typeof(T);
        string typeName = type.ToString();
        if (_uiDic.ContainsKey(typeName))
        {
            YuriUIBehaviour script = _uiDic[typeName];
            script.OnHide();
            script.gameObject.SetActive(false);
            _uiVisibilityDic[script] = false;
            //这个只是暂时加入cache层级,实质上没有被回收
            AddToLayer(script.transform, E_UILayer.CachePool);
            RemoveUIFromActiveUIs(script);
            return;
        }
        else
        {
            Debug.LogError($"_______HideUIError,Can't find UI{type}");
        }
    }

    /// <summary>
    /// 获取某面板
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="type"></param>
    /// <returns></returns>
    public T GetUI<T>() where T : YuriUIBehaviour
    {
        Type type = typeof(T);
        string typeName = type.ToString();

        if (_uiDic.ContainsKey(typeName))
        {
            return _uiDic[typeName] as T;
        }
        else
        {
            //新创一个 默认其为已关闭
            T sc = CreateUI<T>();
            _uiDic.Add(typeName, sc);
            return sc;
        }
    }

    #endregion

    #region UI实例的显示,隐藏与销毁
    public void ShowUI<T>(T ui, params object[] values) where T : YuriUIBehaviour
    {
        _uiVisibilityDic[ui] = true;
        ui.gameObject.SetActive(true);
        AddToLayer(ui.transform, ui.Layer);
        ui.OnShow(values);
        AddUIToActiveUIs(ui);
    }

    public void HideUI<T>(T ui) where T : YuriUIBehaviour
    {
        _uiVisibilityDic[ui] = false;
        ui.gameObject.SetActive(false);
        ui.OnHide();
        AddToCachePool(ui);
        RemoveUIFromActiveUIs(ui);
    }

    public void DestroyUI<T>(T ui) where T : YuriUIBehaviour
    {
        _uiVisibilityDic.Remove(ui);
        string typeName = ui.GetType().ToString();
        if (_uiDic.ContainsKey(typeName) && _uiDic[typeName] == ui)
        {
            _uiDic.Remove(typeName);
        }

        if (_uiCreateDic.ContainsKey(typeName))
        {
            var list = _uiCreateDic[typeName];
            int idx = list.FindIndex((yuriUI) =>
            {
                return yuriUI == ui;
            });
            list.RemoveAt(idx);
        }
        ui.gameObject.SetActive(false);
        RemoveUIFromActiveUIs(ui);
        GameObject.Destroy(ui.gameObject);
    }

    void AddToCachePool(YuriUIBehaviour ui)
    {
        string typeName = ui.GetType().ToString();
        if (_uiPoolDic.ContainsKey(typeName))
        {
            var list = _uiPoolDic[typeName];
            list.Add(ui);
        }
        else
        {
            List<YuriUIBehaviour> list = new() { ui };
            _uiPoolDic.Add(typeName, list);
        }
        AddToLayer(ui.transform, E_UILayer.CachePool);
    }
    #endregion

    #region 已显示的UI的管理
    private void RemoveUIFromActiveUIs(YuriUIBehaviour ui)
    {
        if (_activeUIs.Contains(ui))
        {
            int idx = _activeUIs.FindIndex(activeUI => activeUI == ui);
            _activeUIs.RemoveAt(idx);
        }
    }

    private void AddUIToActiveUIs(YuriUIBehaviour ui)
    {
        if (!_activeUIs.Contains(ui))
        {
            _activeUIs.Add(ui);
        }
    }

    /// <summary>
    /// 隐藏所有正在显示的UI
    /// </summary>
    public void HideAll()
    {
        YuriUIBehaviour[] uis = _activeUIs.ToArray();
        int len = uis.Length;
        for (int i = 0; i < len; i++)
        {
            HideUI(uis[i]);
        }
    }

    /// <summary>
    /// 检查是否是只有局内ui打开了
    /// </summary>
    /// <returns></returns>
    public bool IsOnlyInSessionUIOpen()
    {
        foreach (YuriUIBehaviour ui in _activeUIs)
        {
            if (ui.UIState != E_UIState.InSession)
            {
                return false;
            }
        }
        return true;
    }

    public bool IsOnlyOutOfSessionUIOpen()
    {
        foreach (YuriUIBehaviour ui in _activeUIs)
        {
            if (ui.UIState != E_UIState.OutOfSession)
            {
                return false;
            }
        }
        return true;
    }

    #endregion

    #region 创建相关 
    public T CreateUI<T>() where T : YuriUIBehaviour
    {
        //缓存池里有,直接取一个
        T ui = GetUIFromPool<T>();
        if (ui == null)
        {
            return CreateUIInternal<T>();
        }
        return ui;
    }

    private T CreateUIInternal<T>() where T : YuriUIBehaviour
    {
        Type type = typeof(T);
        string uiLoadPath = GetUILoadPath(type);
        if (uiLoadPath != null)
        {
            GameObject ui = Resources.Load<GameObject>(uiLoadPath);
            ui = GameObject.Instantiate(ui);
            //不激活ui且不删除
            GameObject.DontDestroyOnLoad(ui);
            ui.SetActive(false);
            ui.name = _uiNameDic[uiLoadPath];
            T script = ui.AddComponent(type) as T;
            AddToLayer(ui.transform, E_UILayer.CachePool);
            //立即执行monobehaviour的生命周期函数
            script.Awake();
            script.Start();

            _uiVisibilityDic[script] = false;
            return script;
        }
        return null;
    }

    #endregion

    #region 异步创建相关 - 本地加载应该不存在 先不考虑
    // public void AsyncCreateUI<T>(Type type, UnityAction<T> callBack) where T : YuriUIBehaviour
    // {

    //     //缓存池里有,直接取一个
    //     T ui = GetUIFromPool<T>(type);
    //     if (ui != null)
    //     {
    //         callBack(ui);
    //         return;
    //     }
    //     AsyncCreateUIInternal<T>(type, (script) =>
    //     {

    //         List<YuriUIBehaviour> list = new() { script };
    //         _uiCreateDic.Add(type.ToString(), list);
    //         _uiVisibilityDic[script] = false;
    //         callBack(script);
    //     });
    // }

    // private T AsyncCreate<T>(Type type) where T : YuriUIBehaviour
    // {
    //     string uiLoadPath = GetUILoadPath(type);
    //     ResourceRequest asyncOp = Resources.LoadAsync<T>(uiLoadPath);
    //     asyncOp.allowSceneActivation = false; // 禁止场景激活
    //     int waitCnt = 0;
    //     while (!asyncOp.isDone) // 轮询，直到加载完成
    //     {
    //         // 可以在这里添加一些等待逻辑，如 yield return null;
    //         waitCnt++;
    //         if (waitCnt > 300) //约5s
    //         {
    //             return null;
    //         }
    //     }
    //     return asyncOp.asset as T;
    // }

    // private void AsyncCreateUIInternal<T>(Type type, UnityAction<T> callBack) where T : YuriUIBehaviour
    // {
    //     string uiLoadPath = GetUILoadPath(type);
    //     if (uiLoadPath != null)
    //     {
    //         ResourceMgr.Ins().LoadAsync(uiLoadPath, (GameObject ui) =>
    //                    {
    //                        //不激活ui且不删除
    //                        ui.SetActive(false);
    //                        GameObject.DontDestroyOnLoad(ui);
    //                        T script = ui.AddComponent(type) as T;
    //                        callBack(script);
    //                    });
    //     }
    // }
    #endregion

    /// <summary>
    /// 获取ui的显隐
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="ui"></param>
    /// <returns></returns>
    public bool GetUIVisibility<T>(T ui) where T : YuriUIBehaviour
    {
        if (_uiVisibilityDic.ContainsKey(ui))
        {
            return _uiVisibilityDic[ui];
        }
        Debug.LogError($"______This UI {ui.GetType()} has no reference. Please check its source. Was this UI mistakenly created?");
        return false;
    }

    #region 获取ui路径/添加到某层/从缓存池获取ui
    private string GetUILoadPath(Type type)
    {
        Type rootBase = typeof(YuriUIBehaviour);
        if (!type.IsSubclassOf(rootBase))
        {
            Debug.LogError($"_______CreateUIError,Wrong Type TypeName:{type}");
            return null;
        }

        Type baseType = type;
        //找到母亲是YuriUIBehaviour的那个子类
        while (baseType != null && baseType.BaseType != rootBase)
        {
            baseType = baseType.BaseType;
        }
        if (baseType != null)
        {
            string key = baseType.ToString();
            string uiLoadPath = _uiLoadPathDic[key];
            return uiLoadPath;
        }
        else
        {
            Debug.LogError($"_______CreateUIError,Wrong Type TypeName:{type}");
            return null;
        }
    }

    private void AddToLayer(Transform transform, E_UILayer layer)
    {
        Transform root = _layerDic[layer];
        transform.SetParent(root);

        transform.localPosition = Vector3.zero;
        transform.localScale = Vector3.one;

        (transform as RectTransform).offsetMax = Vector2.zero;
        (transform as RectTransform).offsetMin = Vector2.zero;
        (transform as RectTransform).anchorMax = Vector2.one;
        (transform as RectTransform).anchorMin = Vector2.zero;
    }

    private T GetUIFromPool<T>() where T : YuriUIBehaviour
    {
        Type type = typeof(T);
        string typeName = type.ToString();
        //缓存池里有,直接取一个
        if (_uiPoolDic.ContainsKey(typeName) && _uiPoolDic[typeName].Count > 0)
        {
            for (int i = 0; i < _uiPoolDic[typeName].Count; i++)
            {
                T ui = _uiPoolDic[typeName][i] as T;
                if (ui.IsDestroyed())
                {
                    _uiPoolDic[typeName].RemoveAt(i);
                    continue;
                }
                else
                {
                    _uiPoolDic[typeName].RemoveAt(i);
                    _uiVisibilityDic[ui] = false;
                    return ui;
                }
            }

        }
        return null;
    }

    #endregion

    public override void ClearSingleton()
    {
        _uiLoadPathDic.Clear();
        _uiLoadPathDic = null;

        _uiNameDic.Clear();
        _uiNameDic = null;

        foreach (YuriUIBehaviour script in _uiDic.Values)
        {
            if (!script.IsDestroyed())
            {
                GameObject.Destroy(script.gameObject);
            }
        }
        _uiDic.Clear();
        _uiDic = null;

        foreach (List<YuriUIBehaviour> list in _uiCreateDic.Values)
        {
            foreach (YuriUIBehaviour script in list)
            {
                if (!script.IsDestroyed())
                {
                    GameObject.Destroy(script.gameObject);
                }
            }
            list.Clear();
        }
        _uiCreateDic.Clear();
        _uiCreateDic = null;

        foreach (List<YuriUIBehaviour> list in _uiPoolDic.Values)
        {
            foreach (YuriUIBehaviour script in list)
            {
                if (!script.IsDestroyed())
                {
                    GameObject.Destroy(script.gameObject);
                }
            }
            list.Clear();
        }
        _uiPoolDic.Clear();
        _uiPoolDic = null;

        _uiVisibilityDic.Clear();
        _uiVisibilityDic = null;

        _layerDic.Clear();
        _layerDic = null;
        if (!_rootCanvas.IsDestroyed())
        {
            GameObject.Destroy(_rootCanvas.gameObject);
        }
        // if (!_eventSystem.IsDestroyed())
        // {
        //     GameObject.Destroy(_eventSystem);
        // }
    }

}
