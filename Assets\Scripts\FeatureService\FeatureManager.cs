using System.Collections;
using Common;
using EventObjSystem;
using SideScrollingScene;
using Story;
using UnityCommunity.UnitySingleton;
using UnityEngine;
using static EventsTable;

namespace FeatureService
{
    public class FeatureManager : Singleton<FeatureManager>
    {
        public void TriggerGameFeature(FeatureType featureType, IEventObj eventObj)
        {
            switch (featureType)
            {
                case FeatureType.Battle:
                    break;
                case FeatureType.Story:
                    StoryManager.Instance.StartStory(eventObj.eventCfg);
                    break;
                case FeatureType.Divination:
                    break;
                case FeatureType.ThrowingHolyCups:
                    break;
                case FeatureType.GamblingDen:
                    break;
                case FeatureType.Shop:
                    break;
                case FeatureType.Default:
                    Debug.LogWarning($"TriggerGameFeature:Default eventObj.EventId:{eventObj.EventId}");
                    break;
            }
            if (eventObj != null)
            {
                Debug.Log($"TriggerGameFeature:{eventObj.StyleKey} EventObjType:{EnumUtils.GetName(eventObj.EventObjType)}  EventId:{eventObj.EventId} 效果被触发,立即销毁");
                EventDispatcher.GameEvent.DispatchEvent(EventName.DestroyEventObj, eventObj);
            }
        }
    }
}