using System.Collections;
using EventObjSystem;
using SideScrollingScene;
using Story;
using UnityCommunity.UnitySingleton;
using UnityEngine;
using static EventsTable;

namespace FeatureService
{
    public class FeatureManager : Singleton<FeatureManager>
    {
        public void TriggerGameFeature(FeatureType featureType, IEventObj eventObj)
        {
            switch (featureType)
            {
                case FeatureType.Battle:
                    break;
                case FeatureType.Story:
                    StoryManager.Instance.StartStory(eventObj.eventCfg);
                    break;
                case FeatureType.Divination:
                    break;
                case FeatureType.ThrowingHolyCups:
                    break;
                case FeatureType.GamblingDen:
                    break;
                case FeatureType.Shop:
                    break;
                case FeatureType.Default:
                    Debug.LogWarning($"TriggerGameFeature:Default eventObj.EventId:{eventObj.EventId}");
                    break;
            }
            if (eventObj != null)
            {
                switch (eventObj.EventObjType)
                {
                    case EventObjType.Npc:
                        // MonoMgr.Instance.StartCoroutine(DestroyNpc(eventObj));
                        Debug.Log($" npc EventId:{eventObj.EventId} 效果被触发,立即销毁");
                        BattleSceneManager.Instance.DestroyEventObj(eventObj);
                        // Debug.Log($" npc EventId:{eventObj.EventId} 将在2s后销毁");
                        Debug.Log($"TriggerGameFeature:{EnumUtils.GetName(featureType)} npc.EventId:{eventObj.EventId}");
                        break;
                    case EventObjType.Card:
                        Debug.Log($" card EventId:{eventObj.EventId} 效果被触发,立即销毁");
                        Debug.Log($"TriggerGameFeature:{EnumUtils.GetName(featureType)} card.EventId:{eventObj.EventId}");
                        BattleSceneManager.Instance.DestroyEventObj(eventObj);
                        break;
                    default:
                        break;
                }

            }
        }

        void TriggerBattle(int eventId)
        {

        }

        void TriggerStory(int eventId)
        {

        }

        IEnumerator DestroyNpc(IEventObj obj)
        {
            yield return new WaitForSeconds(2);
            BattleSceneManager.Instance.DestroyEventObj(obj);
        }
    }
}