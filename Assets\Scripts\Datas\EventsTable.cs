//----------------------------------------------
//    Auto Generated. DO NOT edit manually!
//----------------------------------------------

#pragma warning disable 649

using System;
using UnityEngine;

public partial class EventsTable : ScriptableObject {

	public enum FeatureType {
		Default, Story, Divination, ThrowingHolyCups, GamblingDen, Shop, Battle, Random, Roulette, PropEvent
	}

	public enum EventObjType {
		Default, Npc, Card
	}

	[NonSerialized]
	private int mVersion = 1;

	[SerializeField]
	private EventEntry[] _EventEntryItems;

	public EventEntry GetEventEntry(int id) {
		int min = 0;
		int max = _EventEntryItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			EventEntry item = _EventEntryItems[index];
			if (item.id == id) { return item.Init(mVersion, DataGetterObject); }
			if (id < item.id) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	[SerializeField]
	private EventNpcStyle[] _EventNpcStyleItems;

	public EventNpcStyle GetEventNpcStyle(string styleKey) {
		int min = 0;
		int max = _EventNpcStyleItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			EventNpcStyle item = _EventNpcStyleItems[index];
			if (item.styleKey == styleKey) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(styleKey, item.styleKey) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	[SerializeField]
	private EventCardStyle[] _EventCardStyleItems;

	public EventCardStyle GetEventCardStyle(string styleKey) {
		int min = 0;
		int max = _EventCardStyleItems.Length;
		while (min < max) {
			int index = (min + max) >> 1;
			EventCardStyle item = _EventCardStyleItems[index];
			if (item.styleKey == styleKey) { return item.Init(mVersion, DataGetterObject); }
			if (string.Compare(styleKey, item.styleKey) < 0) {
				max = index;
			} else {
				min = index + 1;
			}
		}
		return null;
	}

	public void Reset() {
		mVersion++;
	}

	public interface IDataGetter {
		EventEntry GetEventEntry(int id);
		EventNpcStyle GetEventNpcStyle(string styleKey);
		EventCardStyle GetEventCardStyle(string styleKey);
	}

	private class DataGetter : IDataGetter {
		private Func<int, EventEntry> _GetEventEntry;
		public EventEntry GetEventEntry(int id) {
			return _GetEventEntry(id);
		}
		private Func<string, EventNpcStyle> _GetEventNpcStyle;
		public EventNpcStyle GetEventNpcStyle(string styleKey) {
			return _GetEventNpcStyle(styleKey);
		}
		private Func<string, EventCardStyle> _GetEventCardStyle;
		public EventCardStyle GetEventCardStyle(string styleKey) {
			return _GetEventCardStyle(styleKey);
		}
		public DataGetter(Func<int, EventEntry> getEventEntry, Func<string, EventNpcStyle> getEventNpcStyle, Func<string, EventCardStyle> getEventCardStyle) {
			_GetEventEntry = getEventEntry;
			_GetEventNpcStyle = getEventNpcStyle;
			_GetEventCardStyle = getEventCardStyle;
		}
	}

	[NonSerialized]
	private DataGetter mDataGetterObject;
	private DataGetter DataGetterObject {
		get {
			if (mDataGetterObject == null) {
				mDataGetterObject = new DataGetter(GetEventEntry, GetEventNpcStyle, GetEventCardStyle);
			}
			return mDataGetterObject;
		}
	}
}

[Serializable]
public class EventEntry {

	[SerializeField]
	private int _Id;
	public int id { get { return _Id; } }

	[SerializeField]
	private EventsTable.FeatureType _FeatureType;
	public EventsTable.FeatureType featureType { get { return _FeatureType; } }

	[SerializeField]
	private EventsTable.EventObjType _EventObjType;
	public EventsTable.EventObjType eventObjType { get { return _EventObjType; } }

	[SerializeField]
	private int _NpcId;
	public int npcId { get { return _NpcId; } }

	[SerializeField]
	private string _EventCfg;
	public string eventCfg { get { return _EventCfg; } }

	[SerializeField]
	private bool _IsAutoTrigger;
	public bool IsAutoTrigger { get { return _IsAutoTrigger; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private EventsTable.IDataGetter mGetter;

	public EventEntry Init(int version, EventsTable.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[EventEntry]{{id:{0}, featureType:{1}, eventObjType:{2}, npcId:{3}, eventCfg:{4}, IsAutoTrigger:{5}}}",
			id, featureType, eventObjType, npcId, eventCfg, IsAutoTrigger);
	}

}

[Serializable]
public class EventNpcStyle {

	[SerializeField]
	private string _StyleKey;
	public string styleKey { get { return _StyleKey; } }

	[SerializeField]
	private string _Name;
	public string name { get { return _Name; } }

	[SerializeField]
	private string _UiPath;
	public string uiPath { get { return _UiPath; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private EventsTable.IDataGetter mGetter;

	public EventNpcStyle Init(int version, EventsTable.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[EventNpcStyle]{{styleKey:{0}, name:{1}, uiPath:{2}}}",
			styleKey, name, uiPath);
	}

}

[Serializable]
public class EventCardStyle {

	[SerializeField]
	private string _StyleKey;
	public string styleKey { get { return _StyleKey; } }

	[SerializeField]
	private string _Name;
	public string name { get { return _Name; } }

	[SerializeField]
	private string _CardPath;
	public string cardPath { get { return _CardPath; } }

	[NonSerialized]
	private int mVersion = 0;
	[NonSerialized]
	private EventsTable.IDataGetter mGetter;

	public EventCardStyle Init(int version, EventsTable.IDataGetter getter) {
		if (mVersion == version) { return this; }
		mGetter = getter;
		mVersion = version;
		return this;
	}

	public override string ToString() {
		return string.Format("[EventCardStyle]{{styleKey:{0}, name:{1}, cardPath:{2}}}",
			styleKey, name, cardPath);
	}

}

