import { Class } from "../../../../ugcBase/ares/utils/GlobalFunc";
import { Queue } from "./Queue";
/**
 * 对象Item，用于实现刷新效果
 */
export interface IItemRender {
    /**
     * 设置数据
     * @param {any} data - 数据
     */
    setData(...data: any): void;
    /**
     * 获取UI对象
     * @returns {UserWidget} UI对象
     */
    get uiObject(): UserWidget;
    /**
     * 获取点击对象
     * @returns {StaleButton | Button} 点击对象
     */
    get clickObj(): StaleButton | Button;
    /**
     * 设置选中状态
     * @param {boolean} bool - 是否选中
     */
    setSelect(bool: boolean): void;
    /**
    * 销毁函数
    */
    destroy(): void;
}
/**
 * 无限滚动列表_vae
 */
export class UIMultiScroller {
    /** 当前索引 */
    private _index: number = -1;
    /** 数据数量 */
    private _dataCount: number;
    /** 滚动框 */
    private _sBox: ScrollBox;
    /** 滚动根节点 */
    private _scrollRoot: Canvas;
    /** 滚动方向 */
    private _movement: Orientation;
    /** Item数组 */
    public _itemArr: Array<IItemRender> = [];
    /** 将未显示出来的Item存入未使用队列里面，等待需要使用的时候直接取出 */
    private _unUsedQueue: Queue<IItemRender>;
    /** 每行最大数量 */
    private _maxPerLine: number = 3;
    /** 距离左侧的起始距离 */
    private _leftSpace: number = 30;
    /** 距离上册的起始距离 */
    private _topSpace: number = 30;
    /** Item的宽度 */
    private _cellWidth = 500;
    /** Item的高度 */
    private _cellHeight = 100;
    /** 行间距X */
    private _spacingX = 40;
    /** 行间距Y */
    private _spacingY = 20;
    /** 默认加载行数，一般比可显示行数大2~3行 */
    private _viewLine = 6;
    /** 预制项 */
    private _itemPrefab;
    /** 数据数组 */
    private _dataArray: any[];

    /**
    * 循环列表构造函数
    * @param sbox       ScrollBox对象ui的引用
    * @param sr         ScrollBox下的节点的引用
    * @param prefab     ScrollBoxItem预制体
    * @param maxPerLine 每行显示的数量
    * @param leftSpace  左边界间距
    * @param topSpace   上边界间距
    * @param cellWidth  ScrollBox下子节点的宽
    * @param cellHeight ScrollBox下子节点的高
    * @param viewCount  ScrollBox的默认加载行数
    * @param spacingX   ScrollBox的行间距X
    * @param spacingY   ScrollBox的行间距Y
    */
    constructor(sbox: ScrollBox, sr: Canvas, preafab: Class<IItemRender>, maxPerLine: number, leftSpace: number = 30, topSpace: number = 30, cellWidth: number = 150, cellHeight: number = 150, viewCount: number = 5, spacingX: number = 40, spacingY: number = 20) {
        this._sBox = sbox;
        this._scrollRoot = sr;
        this._itemPrefab = preafab;
        this._leftSpace = leftSpace;
        this._topSpace = topSpace;
        this._movement = sbox.orientation;
        this._cellWidth = cellWidth;
        this._cellHeight = cellHeight;
        this._viewLine = viewCount;
        this._spacingX = spacingX;
        this._spacingY = spacingY;
        this._maxPerLine = maxPerLine;
        this._unUsedQueue = new Queue<IItemRender>();
        this._sBox.onUserScrolled.add((curOffset) => {
            this.onValueChange();
        })
    }

    /**调用InitData第一次初始化时的回调 */
    private mInitCallback: Action2<number, IItemRender> = new Action2();
    /**调用InitData第一次初始化时的回调 */
    public get InitCallback(): Action2<number, IItemRender> {
        return this.mInitCallback;
    }
    /**每个Item刷新时的回调 */
    private mItemCallback: Action2<number, IItemRender> = new Action2();
    /**每个Item刷新时的回调 */
    public get ItemCallback(): Action2<number, IItemRender> {
        return this.mItemCallback;
    }

    /**
     * 设置数据
     * @param val 数据
     */
    setData(val: any[]) {
        this._dataCount = val.length;
        this._dataArray = val;
        this.updateTotalWidth();
        this._index = -1;
        this.resetSBoxPos();
        if (this._itemArr != null) {
            for (let i = this._itemArr.length; i > 0; i--) {
                let item: IItemRender = this._itemArr[i - 1];
                this._itemArr.splice(i - 1, 1);
                this._unUsedQueue.push(item);
                item.uiObject.visibility = (SlateVisibility.Collapsed);
            }
            this.onValueChange();
        }
    }

    /**
     * 数据变化
     * @returns 
     */
    onValueChange() {
        if (this._itemArr == null || this._dataCount == 0) return;
        let index = this.getPosIndex();
        if (index < 0 && this._index > 0) {
            index = 0;
        }
        if (this._index != index && index > -1) {
            this._index = index;
            for (let i = this._itemArr.length; i > 0; i--) {
                let item = this._itemArr[i - 1];
                if (item["scorllIndex"] < index * this._maxPerLine || (item["scorllIndex"] >= (index + this._viewLine) * this._maxPerLine)) {
                    this._itemArr.splice(i - 1, 1);
                    this._unUsedQueue.push(item);
                }
            }
            for (let i = this._index * this._maxPerLine; i < (this._index + this._viewLine) * this._maxPerLine; i++) {
                if (i < 0) continue;
                if (i > this._dataCount - 1) continue;
                let isOk = false;
                for (let item of this._itemArr) {
                    if (item["scorllIndex"] == i) isOk = true;
                }
                if (isOk) continue;
                this.createItem(i);
            }
        }
    }

    /**
     * 根据索引号 获取当前item的位置
     * @param i   索引
     * @return 返回Pos
     */
    getPosition(i: number): Vector2 {
        let xpos = (i % this._maxPerLine);
        let ypos = Math.floor(i / this._maxPerLine);
        switch (this._movement) {
            case Orientation.OrientHorizontal:
                return new Vector2((this._cellWidth + this._spacingX) * ypos + this._leftSpace, ((this._cellHeight + this._spacingY) * xpos) + this._topSpace);
            case Orientation.OrientVertical:
                // return new Vector2(this._cellWidth * xpos + (xpos != 0 ? this._spacingX * xpos : 0) + this._leftSpace, ((this._cellHeight + this._spacingY) * ypos) + this._topSpace);
                return new Vector2((this._cellWidth + this._spacingX) * xpos + this._leftSpace, ((this._cellHeight + this._spacingY) * ypos) + this._topSpace);
            default:
                break;
        }
        return Vector2.zero;
    }

    /**
     * 生命周期销毁 清空Item数组 队列清空
     */
    onDestroy() {
        this._itemArr = null;
        this._unUsedQueue = null;
    }

    /**
     * 获取Item数量、
     * @returns {number} Item数量
     */
    getItemCount(): number {
        return this._maxPerLine * this._viewLine;
    }

    /**
     * 设置Item的索引
     * @param item item对象
     * @param index 索引
     */
    private setItemIndex(item: IItemRender, index: number) {
        item["scorllIndex"] = index;
        item.uiObject.position = (this.getPosition(index));
    }

    /**
     * 创建Item对象
     * @param i 索引
     * @returns 
     */
    private createItem(i: number) {
        let itemBase: IItemRender;
        if (this._unUsedQueue.size() > 0) {
            itemBase = this._unUsedQueue.pop();
            itemBase.uiObject.visibility = (SlateVisibility.Visible);
        }
        else {
            if (this._itemPrefab.Gain != null) {
                itemBase = this._itemPrefab.Gain();
            }
            else {
                itemBase = this._itemPrefab["creat"]();
            }
            (this._scrollRoot.addChild(itemBase.uiObject));
            itemBase.uiObject.size = (new Vector2(this._cellWidth, this._cellHeight));
            this.mInitCallback.call(i, itemBase);
        }
        this.setItemIndex(itemBase, i);
        if (this._dataArray && itemBase["scorllIndex"] < this._dataArray.length) {
            // itemBase.setData(this._dataArray[itemBase["scorllIndex"]]);
            this.mItemCallback.call(i, itemBase);
        }
        this._itemArr.push(itemBase);
        return;
    }

    /**
     * 获取最上位置的索引
     * @return 返回Pos
     */
    private getPosIndex(): number {
        let pos = this._scrollRoot.position;
        switch (this._movement) {
            case Orientation.OrientHorizontal:
                {
                    return Math.floor((pos.x / -(this._cellWidth + this._spacingX)));
                }
            case Orientation.OrientVertical:
                {
                    let ret = pos.y / -(this._cellHeight + this._spacingY);
                    return Math.floor(ret);
                }
            default:
                break;
        }
        return 0;
    }

    /**
     * 这个方法的目的 就是根据总数量 行列 来计算content的真正宽度或者高度
     */
    private updateTotalWidth() {
        switch (this._movement) {
            case Orientation.OrientHorizontal:
                let width = this._cellWidth * this._dataCount + this._spacingX * (this._dataCount - 1);
                let height = this._scrollRoot.size.y;
                this._scrollRoot.size = (new Vector2(width, height));
                break;
            case Orientation.OrientVertical:
                let lineCount = Math.ceil(this._dataCount / this._maxPerLine);
                this._scrollRoot.size = (new Vector2(this._scrollRoot.size.x, this._cellHeight * lineCount + this._spacingY * (lineCount - 1) + this._topSpace));
                break;
            default:
                break;
        }
    }

    /**
     * 重置到顶部（被动触发）
     */
    private resetSBoxPos() { // 默认不会到顶部(暂时还原)
        // 两句配合才能达到重置到顶部的效果
        this._scrollRoot.position = (new Vector2(0, 0));
        this._sBox.scrollToStart();
        // this._sBox.ScrollToStart();
    }

    /**
     * 重置到顶部（主动调用）
     */
    public reset2BoxTop() { // 需要的时候再主动回到顶部(暂未启用)
        // 两句配合才能达到重置到顶部的效果
        this._scrollRoot.position = (new Vector2(0, 0));
        this._sBox.scrollToStart();
    }
}