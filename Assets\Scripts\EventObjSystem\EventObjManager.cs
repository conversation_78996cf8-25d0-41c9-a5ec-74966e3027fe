using CharacterSystem;
using Common;
using Cysharp.Threading.Tasks;
using SideScrollingScene;
using UnityCommunity.UnitySingleton;
using UnityEngine;

namespace EventObjSystem
{

    public class EventObjManager : Singleton<EventObjManager>
    {


        /// <summary>
        /// 创建事件npc
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="npcId"></param>
        /// <returns></returns>
        public async UniTask<EventNpc> CreateEventNpc(int eventId, int npcId)
        {
            try
            {
                Debug.Log($"[EventObjManager] 开始加载EventNpc预制体，路径: {ResourcePathConst.Q_EventNpc}");
                GameObject qNpc = await ResourceLoader.Instance.LoadAsync<GameObject>(ResourcePathConst.Q_EventNpc);

                if (qNpc == null)
                {
                    Debug.LogError($"[EventObjManager] EventNpc预制体加载失败! 路径: {ResourcePathConst.Q_EventNpc}");
                    return null;
                }

                Debug.Log($"[EventObjManager] EventNpc预制体加载成功: {qNpc.name}");

                //等待一帧进行初始化，防止inspector的备选赋值会覆盖init的赋值
                await UniTask.DelayFrame(1);

                CharacterBase characterBase = qNpc.GetComponent<CharacterBase>();
                if (characterBase == null)
                {
                    Debug.LogError($"[EventObjManager] EventNpc预制体上缺少CharacterBase组件! GameObject: {qNpc.name}");
                    return null;
                }

                if (npcId == -1)
                {
                    npcId = 1;
                    Debug.Log($"[EventObjManager] NpcId为-1，自动设置为1");
                }

                Debug.Log($"[EventObjManager] 开始初始化CharacterBase，NpcId: {npcId}");
                characterBase.Initialize(npcId);

                EventNpc eventNpc = qNpc.GetComponent<EventNpc>();
                if (eventNpc == null)
                {
                    Debug.LogError($"[EventObjManager] EventNpc预制体上缺少EventNpc组件! GameObject: {qNpc.name}");
                    return null;
                }

                Debug.Log($"[EventObjManager] 开始初始化EventNpc，EventId: {eventId}");
                eventNpc.Init(eventId);

                Debug.Log($"[EventObjManager] EventNpc创建完成 - EventId: {eventId}, NpcId: {npcId}");
                return eventNpc;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EventObjManager] 创建EventNpc时发生异常 - EventId: {eventId}, NpcId: {npcId}");
                Debug.LogError($"[EventObjManager] 异常详情: {ex.Message}");
                Debug.LogError($"[EventObjManager] 堆栈跟踪: {ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// 创建事件卡牌
        /// </summary>
        /// <param name="eventId"></param>
        /// <returns></returns>
        public async UniTask<EventCard> CreateEventCard(int eventId)
        {
            GameObject eventCard = await ResourceLoader.Instance.LoadAsync<GameObject>(ResourcePathConst.EventCard);
            await UniTask.DelayFrame(1);
            EventCard eventCardComp = eventCard.GetComponent<EventCard>();
            eventCardComp.Init(eventId);
            return eventCardComp;
        }

        public void DestroyEventObj(GameObject eventObj)
        {
            ResourceLoader.Instance.ReleaseInstance(eventObj);
        }
    }
}