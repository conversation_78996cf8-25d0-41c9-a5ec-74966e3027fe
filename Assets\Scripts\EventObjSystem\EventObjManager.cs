using CharacterSystem;
using Common;
using Cysharp.Threading.Tasks;
using SideScrollingScene;
using UnityCommunity.UnitySingleton;
using UnityEngine;

namespace EventObjSystem
{

    public class EventObjManager : Singleton<EventObjManager>
    {


        /// <summary>
        /// 创建事件npc
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="npcId"></param>
        /// <returns></returns>
        public async UniTask<EventNpc> CreateEventNpc(int eventId, int npcId, EventSpawnPoint point = null)
        {
            GameObject qNpc = await ResourceLoader.Instance.LoadAsync<GameObject>(ResourcePathConst.Q_EventNpc);
            //等待一帧进行初始化，防止inspector的备选赋值会覆盖init的赋值
            await UniTask.DelayFrame(1);
            CharacterBase characterBase = qNpc.GetComponent<CharacterBase>();
            if (npcId == -1)
            {
                npcId = 1;
            }
            characterBase.Initialize(npcId);
            EventNpc eventNpc = qNpc.GetComponent<EventNpc>();
            if (point != null)
            {
                eventNpc.InitByPoint(point);
            }
            else
            {
                eventNpc.InitByRandomId(eventId);
            }
            return eventNpc;
        }

        /// <summary>
        /// 创建事件卡牌
        /// </summary>
        /// <param name="eventId"></param>
        /// <returns></returns>
        public async UniTask<EventCard> CreateEventCard(int eventId, EventSpawnPoint point = null)
        {
            GameObject eventCard = await ResourceLoader.Instance.LoadAsync<GameObject>(ResourcePathConst.EventCard);
            await UniTask.DelayFrame(1);
            EventCard eventCardComp = eventCard.GetComponent<EventCard>();
            if (point != null)
            {
                eventCardComp.InitByPoint(point);
            }
            else
            {
                eventCardComp.InitByRandomId(eventId);
            }
            return eventCardComp;
        }

        public void DestroyEventObj(GameObject eventObj)
        {
            ResourceLoader.Instance.ReleaseInstance(eventObj);
        }
    }
}