using UnityEngine;
using UnityEngine.EventSystems;

public abstract class ConfirmPanelGenerated : <PERSON><PERSON><PERSON>eh<PERSON><PERSON>
{
    // 节点变量
    protected TMPro.TextMeshProUGUI tm_btnConfirm;
    protected TMPro.TextMeshProUGUI tm_btnCancel;
    protected TMPro.TextMeshProUGUI tm_txt_Comfirm;


    public override void Awake()
    {
        // 初始化节点
        tm_btnConfirm = transform.Find("btnConfirm").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        tm_btnCancel = transform.Find("btnCancel").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        tm_txt_Comfirm = transform.Find("txt_Comfirm").gameObject.GetComponent<TMPro.TextMeshProUGUI>();


        // 给按钮添加一个初始点击事件


    }

    public abstract override void OnShow(params dynamic[] values);
    public abstract override void OnHide();



}
