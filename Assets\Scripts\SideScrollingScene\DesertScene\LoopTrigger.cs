using UnityEngine;

namespace SideScrollingScene
{

    public class LoopTrigger : MonoBeh<PERSON>our
    {
        void OnTriggerEnter2D(Collider2D collision)
        {
            if (collision.gameObject.CompareTag("Player"))
            {
                collision.gameObject.transform.parent = transform;
                DesertLoopSceneManager.Instance.Switch();
            }
        }

        void OnTriggerExit2D(Collider2D collision)
        {
            if (collision.gameObject.CompareTag("Player"))
            {
                collision.gameObject.transform.parent = null;
                DesertLoopSceneManager.Instance.Switch();
            }
        }
    }
}