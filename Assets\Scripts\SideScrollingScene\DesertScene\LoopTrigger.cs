using UnityEngine;

namespace SideScrollingScene
{

    public enum LoopTriggerType
    {
        LoopArea,
        NormalArea
    }

    public class LoopTrigger : MonoBehaviour
    {
        public GameObject LoopArea;
        public LoopTriggerType TriggerType = LoopTriggerType.NormalArea;
        void OnTriggerEnter2D(Collider2D collision)
        {
            if (collision.gameObject.CompareTag("Player"))
            {
                switch (TriggerType)
                {
                    case LoopTriggerType.NormalArea:
                        DesertLoopSceneManager.Instance.SwitchForceByNormalArea(true);
                        break;
                    case LoopTriggerType.LoopArea:
                        break;
                }
            }
        }

        void OnTriggerExit2D(Collider2D collision)
        {
            if (collision.gameObject.CompareTag("Player"))
            {
                // 延迟到下一帧执行，避免与FixedUpdate冲突
                StartCoroutine(DelayedTriggerAction());
            }
        }

        private System.Collections.IEnumerator DelayedTriggerAction()
        {
            // 等待当前帧结束
            yield return new WaitForEndOfFrame();

            switch (TriggerType)
            {
                case LoopTriggerType.NormalArea:
                    DesertLoopSceneManager.Instance.SwitchForceByNormalArea(false);
                    break;
                case LoopTriggerType.LoopArea:
                    DesertLoopSceneManager.Instance.SwitchForceByLeaveLoopArea();
                    break;
            }
        }
    }
}