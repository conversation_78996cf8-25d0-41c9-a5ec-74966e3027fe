using UnityEngine;

namespace SideScrollingScene
{

    public enum LoopTriggerType
    {
        LoopArea,
        NormalArea
    }

    public class LoopTrigger : MonoBehaviour
    {
        public GameObject LoopArea;
        public LoopTriggerType TriggerType = LoopTriggerType.NormalArea;
        void OnTriggerEnter2D(Collider2D collision)
        {
            if (collision.gameObject.CompareTag("Player"))
            {
                switch (TriggerType)
                {
                    case LoopTriggerType.NormalArea:
                        DesertLoopSceneManager.Instance.SwitchForceByNormalArea(true);
                        break;
                    case LoopTriggerType.LoopArea:
                        break;
                }
            }
        }

        void OnTriggerExit2D(Collider2D collision)
        {
            if (collision.gameObject.CompareTag("Player"))
            {
                switch (TriggerType)
                {
                    case LoopTriggerType.NormalArea:
                        DesertLoopSceneManager.Instance.SwitchForceByNormalArea(false);
                        break;
                    case LoopTriggerType.LoopArea:
                        DesertLoopSceneManager.Instance.SwitchForceByLeaveLoopArea();
                        break;
                }
            }
        }
    }
}