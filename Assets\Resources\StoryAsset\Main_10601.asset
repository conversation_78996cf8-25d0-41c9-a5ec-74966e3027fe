%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-5057292158282851142
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 102a0f24c68942f2902390112386f7f5, type: 3}
  m_Name: Finish
  m_EditorClassIdentifier: 
  graph: {fileID: 11400000}
  position: {x: 920, y: 536}
  ports:
    keys:
    - output
    - input
    values:
    - _fieldName: output
      _node: {fileID: -5057292158282851142}
      _typeQualifiedName: StoryEditor.Empty, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
        PublicKeyToken=null
      connections: []
      _direction: 1
      _connectionType: 0
      _typeConstraint: 0
      _dynamic: 0
    - _fieldName: input
      _node: {fileID: -5057292158282851142}
      _typeQualifiedName: StoryEditor.Empty, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
        PublicKeyToken=null
      connections:
      - fieldName: output
        node: {fileID: 5341741464539436657}
        reroutePoints: []
      _direction: 0
      _connectionType: 0
      _typeConstraint: 0
      _dynamic: 0
--- !u!114 &-38892502486491658
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b24f883662c43cf9c023d57a5940984, type: 3}
  m_Name: Start
  m_EditorClassIdentifier: 
  graph: {fileID: 11400000}
  position: {x: 424, y: 536}
  ports:
    keys:
    - output
    values:
    - _fieldName: output
      _node: {fileID: -38892502486491658}
      _typeQualifiedName: StoryEditor.Empty, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
        PublicKeyToken=null
      connections:
      - fieldName: input
        node: {fileID: 5341741464539436657}
        reroutePoints: []
      _direction: 1
      _connectionType: 0
      _typeConstraint: 0
      _dynamic: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 417942112217b284ba8bb69171d00f12, type: 3}
  m_Name: Main_10601
  m_EditorClassIdentifier: 
  nodes:
  - {fileID: -38892502486491658}
  - {fileID: 5341741464539436657}
  - {fileID: -5057292158282851142}
--- !u!114 &5341741464539436657
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 40b384264165464282e7c49787e15270, type: 3}
  m_Name: Excel Dialog
  m_EditorClassIdentifier: 
  graph: {fileID: 11400000}
  position: {x: 664, y: 536}
  ports:
    keys:
    - input
    - output
    values:
    - _fieldName: input
      _node: {fileID: 5341741464539436657}
      _typeQualifiedName: StoryEditor.Empty, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
        PublicKeyToken=null
      connections:
      - fieldName: output
        node: {fileID: -38892502486491658}
        reroutePoints: []
      _direction: 0
      _connectionType: 0
      _typeConstraint: 0
      _dynamic: 0
    - _fieldName: output
      _node: {fileID: 5341741464539436657}
      _typeQualifiedName: StoryEditor.Empty, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
        PublicKeyToken=null
      connections:
      - fieldName: input
        node: {fileID: -5057292158282851142}
        reroutePoints: []
      _direction: 1
      _connectionType: 0
      _typeConstraint: 0
      _dynamic: 0
  excelDialogID: 10601
