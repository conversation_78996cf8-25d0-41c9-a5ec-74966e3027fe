using UnityEngine;
using UnityEngine.EventSystems;

public abstract class NpcSelectItemGenerated : <PERSON><PERSON><PERSON>eh<PERSON><PERSON>
{
    // 节点变量
    protected UnityEngine.UI.Image img_icon;
    protected Transform node_intro;
    protected TMPro.TextMeshProUGUI tm_reason;
    protected TMPro.TextMeshProUGUI tm_cantSelected;


    public override void Awake()
    {
        // 初始化节点
        img_icon = transform.Find("icon").gameObject.GetComponent<UnityEngine.UI.Image>();
        node_intro = transform.Find("node_intro");
        tm_reason = transform.Find("node_intro/reason").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        tm_cantSelected = transform.Find("node_intro/cantSelected").gameObject.GetComponent<TMPro.TextMeshProUGUI>();


        // 给按钮添加一个初始点击事件


    }

    public abstract override void OnHide();



}
