using UnityEngine;
using UnityEngine.EventSystems;

public abstract class SelectPanelGenerated : Yuri<PERSON><PERSON>ehavi<PERSON>
{
    // 节点变量
    protected UnityEngine.UI.Image img_bg;
    protected Transform node_bottom;
    protected Transform node_top;
    protected UnityEngine.UI.Image img_mask;
    protected Transform node_System;
    protected UnityEngine.UI.Button btn_close;
    protected TMPro.TextMeshProUGUI tm_btn_txt;
    protected TMPro.TextMeshProUGUI tm_num_fortune;
    protected TMPro.TextMeshProUGUI tm_num_coin;
    protected TMPro.TextMeshProUGUI tm_num_energy;
    protected UnityEngine.UI.Image img_go;
    protected UnityEngine.UI.Button btn_go;


    public override void Awake()
    {
        // 初始化节点
        img_bg = transform.Find("bg").gameObject.GetComponent<UnityEngine.UI.Image>();
        node_bottom = transform.Find("node_bottom");
        node_top = transform.Find("node_top");
        img_mask = transform.Find("node_top/mask").gameObject.GetComponent<UnityEngine.UI.Image>();
        node_System = transform.Find("node_System");
        btn_close = transform.Find("node_System/close").gameObject.GetComponent<UnityEngine.UI.Button>();
        tm_btn_txt = transform.Find("node_System/close/btn_txt").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        tm_num_fortune = transform.Find("node_System/Resource_pnl/Resource_item/num_fortune").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        tm_num_coin = transform.Find("node_System/Resource_pnl/Resource_item (1)/num_coin").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        tm_num_energy = transform.Find("node_System/Resource_pnl/Resource_item (2)/num_energy").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        img_go = transform.Find("node_System/go").gameObject.GetComponent<UnityEngine.UI.Image>();
        btn_go = transform.Find("node_System/go").gameObject.GetComponent<UnityEngine.UI.Button>();


        // 给按钮添加一个初始点击事件
        btn_close.onClick.AddListener(() =>
        {
            EventDispatcher.GameEvent.DispatchEvent("UIBtnOnClick", btn_close.name , GetType().ToString());
            Debug.Log($"_______UIBtnOnClick,name:btn_close typeName:{GetType()}");
        });
        btn_go.onClick.AddListener(() =>
        {
            EventDispatcher.GameEvent.DispatchEvent("UIBtnOnClick", btn_go.name , GetType().ToString());
            Debug.Log($"_______UIBtnOnClick,name:btn_go typeName:{GetType()}");
        });


    }

    public abstract override void OnHide();



}
