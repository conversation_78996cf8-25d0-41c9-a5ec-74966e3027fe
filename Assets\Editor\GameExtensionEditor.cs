using System.IO;
using SideScrollingScene;
using UnityEditor;
using UnityEngine;

namespace GameEditorExtensions
{
    public static class Menu
    {
        [MenuItem("工具/横版场景/注入场景块数据", priority = 0)]
        public static void AutoInitChunkScene()
        {
            string resourcePath = "Resources/";
            GameObject[] chunks = GameObject.FindGameObjectsWithTag("SceneChunk");
            ChunkStreamingManager ChunkMgr =
            GameObject.FindAnyObjectByType<ChunkStreamingManager>()?.GetComponent<ChunkStreamingManager>();
            if (ChunkMgr == null)
            {
                ChunkMgr = GameObject.FindAnyObjectByType<BattleSceneManager>()?.GetComponent<BattleSceneManager>();
            }
            else if (ChunkMgr == null)
            {
                ChunkMgr = GameObject.FindAnyObjectByType<NormalSceneManager>()?.GetComponent<NormalSceneManager>();
            }
            ChunkMgr.SceneChunks.Clear();
            foreach (var sceneChunk in chunks)
            {
                Chunk chunk = sceneChunk.GetComponent<Chunk>();
                ChunkMgr.SceneChunks.Add(chunk);
                //注入场景sprite数据
                var sprite = sceneChunk.GetComponent<SpriteRenderer>()?.sprite;
                if (sprite != null)
                {
                    string fullPath = AssetDatabase.GetAssetPath(sprite);
                    string name = Path.GetFileNameWithoutExtension(fullPath);
                    fullPath = fullPath.Replace("\\", "/"); // Windows 路径兼容
                    int index = fullPath.IndexOf(resourcePath);
                    if (index >= 0)
                    {
                        string relative = fullPath.Substring(index + resourcePath.Length);
                        relative = Path.ChangeExtension(relative, null);
                        chunk.SpritePath = relative;
                    }
                    sceneChunk.name = name;
                }

                chunk.RandomEventPoints.Clear();
                chunk.FixedEventPoints.Clear();
                chunk.PlayerSpawnPoints.Clear();

                //注入玩家出生点数据
                foreach (Transform child in sceneChunk.transform)
                {

                    if (child.tag.Equals("EventSpawnPoint"))
                    {
                        EventSpawnPoint point = child.GetComponent<EventSpawnPoint>();
                        if (point.IsRandomSpawn)
                        {
                            chunk.RandomEventPoints.Add(point);
                        }
                        else
                        {
                            if (point.EventObjType == EventsTable.EventObjType.Default)
                            {
                                point.EventObjType = (ChunkMgr as BattleSceneManager).SceneEventObjType;
                            }
                            chunk.FixedEventPoints.Add(point);
                        }
                    }

                    if (child.tag.Equals("PlayerSpawnPoint"))
                    {
                        chunk.PlayerSpawnPoints.Add(child);
                    }
                }
            }

            Debug.Log("注入场景块数据完成！");
        }

    }
}