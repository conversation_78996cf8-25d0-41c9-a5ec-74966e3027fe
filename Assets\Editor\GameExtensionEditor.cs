using System.IO;
using SideScrollingScene;
using UnityEditor;
using UnityEngine;

namespace GameEditorExtensions
{
    public static class Menu
    {
        [MenuItem("工具/横版场景/注入场景块数据", priority = 0)]
        public static void AutoInitChunkScene()
        {
            string resourcePath = "Resources/";
            GameObject[] chunks = GameObject.FindGameObjectsWithTag("SceneChunk");
            ChunkStreamingManager chunkMgr = GameObject.FindAnyObjectByType<ChunkStreamingManager>();
            chunkMgr.SceneChunks.Clear();
            foreach (var sceneChunk in chunks)
            {
                Chunk chunk = sceneChunk.GetComponent<Chunk>();
                chunkMgr.SceneChunks.Add(chunk);
                //注入场景sprite数据
                SpriteRenderer spriteRenderer = sceneChunk.GetComponent<SpriteRenderer>();
                Sprite sprite = spriteRenderer != null ? spriteRenderer.sprite : null;
                if (sprite != null)
                {
                    string fullPath = AssetDatabase.GetAssetPath(sprite);
                    string name = Path.GetFileNameWithoutExtension(fullPath);
                    fullPath = fullPath.Replace("\\", "/"); // Windows 路径兼容
                    int index = fullPath.IndexOf(resourcePath);
                    if (index >= 0)
                    {
                        string relative = fullPath[(index + resourcePath.Length)..];
                        relative = Path.ChangeExtension(relative, null);
                        chunk.SpritePath = relative;
                    }
                    sceneChunk.name = name;
                }

                chunk.RandomEventPoints.Clear();
                chunk.FixedEventPoints.Clear();
                chunk.PlayerSpawnPoints.Clear();

                //注入玩家出生点数据
                foreach (Transform child in sceneChunk.transform)
                {

                    if (child.CompareTag("EventSpawnPoint"))
                    {
                        EventSpawnPoint point = child.GetComponent<EventSpawnPoint>();
                        if (point.IsRandomSpawn)
                        {
                            chunk.RandomEventPoints.Add(point);
                        }
                        else
                        {
                            if (point.EventObjType == EventsTable.EventObjType.Default)
                            {
                                point.EventObjType = (chunkMgr as BattleSceneManager).SceneEventObjType;
                            }
                            chunk.FixedEventPoints.Add(point);
                        }
                    }

                    if (child.CompareTag("PlayerSpawnPoint"))
                    {
                        chunk.PlayerSpawnPoints.Add(child);
                    }
                }
            }

            Debug.Log("注入场景块数据完成！");
        }

    }
}