using System;
using System.Collections.Generic;
using Common;
using StoryCommon;
using StoryEditor;
using UI;
using UnityCommunity.UnitySingleton;
using UnityEngine;

namespace Story
{
    public class StoryManager : MonoSingleton<StoryManager>
    {
        private string _storyID;
        public GameObject StoryViewPrefab;
        public GameObject StoryOptionPrefab;
        public GameObject StoryReviewPrefab;

        private StoryView _storyView = null;
        private StoryOptionView _storyOptionView = null;
        private StoryReviewView _storyReviewView = null;

        [SerializeField]
        private StoryGraph _storyGraph;

        public StoryGraph storyGraph => _storyGraph;

        public List<object> reviewInfoList = new List<object>();

        public void AddReviewInfoToList(object reviewInfo)
        {
            reviewInfoList.Add(reviewInfo);
        }

        public void StartStory(string storyID)
        {
            _storyID = storyID;
            SetStoryViewVisible(true);
            StoryGraph storyGraph = ResourceLoader.Instance.Load<StoryGraph>("StoryAsset/" + storyID);
            _storyGraph = storyGraph;
            _storyGraph.Start();
        }

        public void SetStoryViewVisible(bool flag)
        {
            if (_storyView == null)
            {
                var storyRoot = UIManager.Instance.GetViewRoot(ViewRootEnum.STORY);
                var go = GameObject.Instantiate(StoryViewPrefab, storyRoot.transform, true);
                var rectTrans = go.transform as RectTransform;
                rectTrans.offsetMin = Vector2.zero;
                rectTrans.offsetMax = Vector2.zero;
                go.transform.localPosition = Vector3.zero;
                go.transform.localScale = Vector3.one;
                _storyView = go.GetComponent<StoryView>();
            }
            _storyView.transform.SetAsLastSibling();
            _storyView.gameObject.SetActive(flag);
        }

        public void SetStoryOptionVisible(bool flag)
        {
            if (_storyOptionView == null)
            {
                var storyRoot = UIManager.Instance.GetViewRoot(ViewRootEnum.STORY);
                var go = GameObject.Instantiate(StoryOptionPrefab, storyRoot.transform, true);
                var rectTrans = go.transform as RectTransform;
                rectTrans.offsetMin = Vector2.zero;
                rectTrans.offsetMax = Vector2.zero;
                go.transform.localPosition = Vector3.zero;
                go.transform.localScale = Vector3.one;
                _storyOptionView = go.GetComponent<StoryOptionView>();
            }
            _storyOptionView.transform.SetAsLastSibling();
            _storyOptionView.gameObject.SetActive(flag);
        }

        public void SetStoryReviewVisible(bool flag)
        {
            if (_storyReviewView == null)
            {
                var storyRoot = UIManager.Instance.GetViewRoot(ViewRootEnum.STORY);
                var go = GameObject.Instantiate(StoryReviewPrefab, storyRoot.transform, true);
                var rectTrans = go.transform as RectTransform;
                rectTrans.offsetMin = Vector2.zero;
                rectTrans.offsetMax = Vector2.zero;
                go.transform.localPosition = Vector3.zero;
                go.transform.localScale = Vector3.one;
                _storyReviewView = go.GetComponent<StoryReviewView>();
            }
            _storyReviewView.transform.SetAsLastSibling();
            _storyReviewView.gameObject.SetActive(flag);
            _storyReviewView.BuildData(reviewInfoList);
        }

        public void ShowDialog(DialogInfo dialogInfo, Action onDialogFinish)
        {
            AddReviewInfoToList(dialogInfo);
            _storyView.ShowDialog(dialogInfo, onDialogFinish);
        }

        public void SkipDialogToComplete()
        {
            _storyView.SkipToComplete();
        }

        public void ShowOption(List<string> optionList, Action<string> onChoiceOption)
        {
            SetStoryOptionVisible(true);
            _storyOptionView.ShowOptions(optionList, onChoiceOption);
        }

    }
}