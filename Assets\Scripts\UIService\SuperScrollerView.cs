using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;


/// <summary>
/// 对象Item，用于实现刷新效果
/// </summary>
public interface IItemRender
{
    // 滚动索引
    public int ScrollIndex { get; set; }
    /// <summary>
    /// 设置数据
    /// </summary>
    /// <param name="data">数据</param>
    void SetData(params object[] data);

    /// <summary>
    /// 获取UI对象
    /// </summary>
    GameObject UIObject { get; }

    /// <summary>
    /// 获取点击对象
    /// </summary>
    Button ClickObj { get; }

    /// <summary>
    /// 设置选中状态
    /// </summary>
    /// <param name="isSelected">是否选中</param>
    void SetSelect(bool isSelected);

    /// <summary>
    /// 销毁函数
    /// </summary>
    void Destroy();
}

/// <summary>
/// 无限滚动列表
/// </summary>
public class UIMultiScroller
{
    /// <summary>当前索引</summary>
    private int _index = -1;

    /// <summary>数据数量</summary>
    private int _dataCount;

    /// <summary>滚动框</summary>
    private ScrollRect _scrollRect;

    /// <summary>滚动根节点</summary>
    private RectTransform _scrollRoot;

    /// <summary>滚动方向</summary>
    private ScrollRect.MovementType _movement;

    /// <summary>Item数组</summary>
    public List<IItemRender> _itemArr = new List<IItemRender>();

    /// <summary>将未显示出来的Item存入未使用队列里面，等待需要使用的时候直接取出</summary>
    private Queue<IItemRender> _unUsedQueue;

    /// <summary>每行最大数量</summary>
    private int _maxPerLine = 3;

    /// <summary>距离左侧的起始距离</summary>
    private float _leftSpace = 30f;

    /// <summary>距离上册的起始距离</summary>
    private float _topSpace = 30f;

    /// <summary>Item的宽度</summary>
    private float _cellWidth = 500f;

    /// <summary>Item的高度</summary>
    private float _cellHeight = 100f;

    /// <summary>行间距X</summary>
    private float _spacingX = 40f;

    /// <summary>行间距Y</summary>
    private float _spacingY = 20f;

    /// <summary>默认加载行数，一般比可显示行数大2~3行</summary>
    private int _viewLine = 6;

    /// <summary>预制项</summary>
    private System.Type _itemPrefabType;

    /// <summary>数据数组</summary>
    private object[] _dataArray;

    /// <summary>滚动方向枚举</summary>
    public enum ScrollDirection
    {
        Horizontal,
        Vertical
    }

    private ScrollDirection _scrollDirection;

    /// <summary>调用InitData第一次初始化时的回调</summary>
    public event System.Action<int, IItemRender> InitCallback;

    /// <summary>每个Item刷新时的回调</summary>
    public event System.Action<int, IItemRender> ItemCallback;

    /// <summary>
    /// 循环列表构造函数
    /// </summary>
    /// <param name="scrollRect">ScrollRect对象ui的引用</param>
    /// <param name="scrollRoot">ScrollRect下的节点的引用</param>
    /// <param name="itemPrefabType">ScrollRectItem预制体类型</param>
    /// <param name="maxPerLine">每行显示的数量</param>
    /// <param name="leftSpace">左边界间距</param>
    /// <param name="topSpace">上边界间距</param>
    /// <param name="cellWidth">ScrollRect下子节点的宽</param>
    /// <param name="cellHeight">ScrollRect下子节点的高</param>
    /// <param name="viewCount">ScrollRect的默认加载行数</param>
    /// <param name="spacingX">ScrollRect的行间距X</param>
    /// <param name="spacingY">ScrollRect的行间距Y</param>
    public UIMultiScroller(ScrollRect scrollRect, RectTransform scrollRoot, Type itemPrefabType,
        int maxPerLine, float leftSpace = 30f, float topSpace = 30f, float cellWidth = 150f,
        float cellHeight = 150f, int viewCount = 5, float spacingX = 40f, float spacingY = 20f)
    {
        _scrollRect = scrollRect;
        _scrollRoot = scrollRoot;
        _itemPrefabType = itemPrefabType;
        _leftSpace = leftSpace;
        _topSpace = topSpace;
        _scrollDirection = scrollRect.horizontal ? ScrollDirection.Horizontal : ScrollDirection.Vertical;
        _cellWidth = cellWidth;
        _cellHeight = cellHeight;
        _viewLine = viewCount;
        _spacingX = spacingX;
        _spacingY = spacingY;
        _maxPerLine = maxPerLine;
        _unUsedQueue = new Queue<IItemRender>();

        // 添加滚动监听
        _scrollRect.onValueChanged.AddListener(OnValueChange);
    }

    /// <summary>
    /// 设置数据
    /// </summary>
    /// <param name="val">数据</param>
    public void SetData(object[] val)
    {
        _dataCount = val.Length;
        _dataArray = val;
        UpdateTotalSize();
        _index = -1;
        ResetScrollPos();

        if (_itemArr != null)
        {
            for (int i = _itemArr.Count - 1; i >= 0; i--)
            {
                IItemRender item = _itemArr[i];
                _itemArr.RemoveAt(i);
                _unUsedQueue.Enqueue(item);
                item.UIObject.SetActive(false);
            }
            OnValueChange(Vector2.zero);
        }
    }

    /// <summary>
    /// 数据变化
    /// </summary>
    private void OnValueChange(Vector2 scrollValue)
    {
        if (_itemArr == null || _dataCount == 0) return;

        int index = GetPosIndex();
        if (index < 0 && _index > 0)
        {
            index = 0;
        }

        if (_index != index && index > -1)
        {
            _index = index;

            // 移除不在视野内的Item
            for (int i = _itemArr.Count - 1; i >= 0; i--)
            {
                IItemRender item = _itemArr[i];
                int scrollIndex = GetItemScrollIndex(item);
                if (scrollIndex < index * _maxPerLine || scrollIndex >= (index + _viewLine) * _maxPerLine)
                {
                    _itemArr.RemoveAt(i);
                    _unUsedQueue.Enqueue(item);
                    item.UIObject.SetActive(false);
                }
            }

            // 创建需要显示的Item
            for (int i = _index * _maxPerLine; i < (_index + _viewLine) * _maxPerLine; i++)
            {
                if (i < 0) continue;
                if (i > _dataCount - 1) continue;

                bool isExist = false;
                foreach (IItemRender item in _itemArr)
                {
                    if (GetItemScrollIndex(item) == i)
                    {
                        isExist = true;
                        break;
                    }
                }

                if (!isExist)
                {
                    CreateItem(i);
                }
            }
        }
    }

    /// <summary>
    /// 根据索引号获取当前item的位置
    /// </summary>
    /// <param name="i">索引</param>
    /// <returns>返回位置</returns>
    private Vector2 GetPosition(int i)
    {
        int xpos = i % _maxPerLine;
        int ypos = i / _maxPerLine;

        switch (_scrollDirection)
        {
            case ScrollDirection.Horizontal:
                return new Vector2((_cellWidth + _spacingX) * ypos + _leftSpace,
                                 (_cellHeight + _spacingY) * xpos + _topSpace);
            case ScrollDirection.Vertical:
                return new Vector2((_cellWidth + _spacingX) * xpos + _leftSpace,
                                 (_cellHeight + _spacingY) * ypos + _topSpace);
            default:
                return Vector2.zero;
        }
    }

    /// <summary>
    /// 生命周期销毁 清空Item数组 队列清空
    /// </summary>
    public void OnDestroy()
    {
        _itemArr?.Clear();
        _unUsedQueue?.Clear();
        _itemArr = null;
        _unUsedQueue = null;
    }

    /// <summary>
    /// 获取Item数量
    /// </summary>
    /// <returns>Item数量</returns>
    public int GetItemCount()
    {
        return _maxPerLine * _viewLine;
    }

    /// <summary>
    /// 设置Item的索引
    /// </summary>
    /// <param name="item">item对象</param>
    /// <param name="index">索引</param>
    private void SetItemIndex(IItemRender item, int index)
    {
        SetItemScrollIndex(item, index);
        RectTransform rectTransform = item.UIObject.GetComponent<RectTransform>();
        rectTransform.anchoredPosition = GetPosition(index);
    }

    /// <summary>
    /// 创建Item对象
    /// </summary>
    /// <param name="i">索引</param>
    private void CreateItem(int i)
    {
        IItemRender itemBase;

        if (_unUsedQueue.Count > 0)
        {
            itemBase = _unUsedQueue.Dequeue();
            itemBase.UIObject.SetActive(true);
        }
        else
        {
            // 创建新的Item实例
            GameObject itemObj = new GameObject($"ScrollItem_{i}");
            itemBase = (IItemRender)System.Activator.CreateInstance(_itemPrefabType);

            // 设置父级和基本属性
            itemObj.transform.SetParent(_scrollRoot, false);
            RectTransform rectTransform = itemObj.GetComponent<RectTransform>() ?? itemObj.AddComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(_cellWidth, _cellHeight);

            InitCallback?.Invoke(i, itemBase);
        }

        SetItemIndex(itemBase, i);

        if (_dataArray != null && GetItemScrollIndex(itemBase) < _dataArray.Length)
        {
            ItemCallback?.Invoke(i, itemBase);
        }

        _itemArr.Add(itemBase);
    }

    /// <summary>
    /// 获取最上位置的索引
    /// </summary>
    /// <returns>返回索引</returns>
    private int GetPosIndex()
    {
        Vector2 pos = _scrollRoot.anchoredPosition;

        switch (_scrollDirection)
        {
            case ScrollDirection.Horizontal:
                return Mathf.FloorToInt(pos.x / -(_cellWidth + _spacingX));
            case ScrollDirection.Vertical:
                float ret = pos.y / -(_cellHeight + _spacingY);
                return Mathf.FloorToInt(ret);
            default:
                return 0;
        }
    }

    /// <summary>
    /// 根据总数量行列来计算content的真正宽度或者高度
    /// </summary>
    private void UpdateTotalSize()
    {
        switch (_scrollDirection)
        {
            case ScrollDirection.Horizontal:
                float width = _cellWidth * _dataCount + _spacingX * (_dataCount - 1);
                float height = _scrollRoot.sizeDelta.y;
                _scrollRoot.sizeDelta = new Vector2(width, height);
                break;
            case ScrollDirection.Vertical:
                int lineCount = Mathf.CeilToInt((float)_dataCount / _maxPerLine);
                _scrollRoot.sizeDelta = new Vector2(_scrollRoot.sizeDelta.x,
                    _cellHeight * lineCount + _spacingY * (lineCount - 1) + _topSpace);
                break;
        }
    }

    /// <summary>
    /// 重置到顶部（被动触发）
    /// </summary>
    private void ResetScrollPos()
    {
        _scrollRoot.anchoredPosition = Vector2.zero;
        _scrollRect.normalizedPosition = Vector2.one;
    }

    /// <summary>
    /// 重置到顶部（主动调用）
    /// </summary>
    public void Reset2BoxTop()
    {
        _scrollRoot.anchoredPosition = Vector2.zero;
        _scrollRect.normalizedPosition = Vector2.one;
    }

    /// <summary>
    /// 获取Item的滚动索引
    /// </summary>
    private int GetItemScrollIndex(IItemRender item)
    {
        return item.ScrollIndex;
    }

    /// <summary>
    /// 设置Item的滚动索引
    /// </summary>
    private void SetItemScrollIndex(IItemRender item, int index)
    {
        item.ScrollIndex = index;
    }
}

