using UnityEngine;
using UnityEngine.EventSystems;

public abstract class SkillItemGenerated : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{
    // 节点变量
    protected UnityEngine.UI.Image img_skillItem;
    protected UnityEngine.UI.Button btn_skillItem;


    public override void Awake()
    {
        // 初始化节点
        img_skillItem = transform.Find("skillItem").gameObject.GetComponent<UnityEngine.UI.Image>();
        btn_skillItem = transform.Find("skillItem").gameObject.GetComponent<UnityEngine.UI.Button>();


        // 给按钮添加一个初始点击事件
        btn_skillItem.onClick.AddListener(() =>
        {
            EventDispatcher.GameEvent.DispatchEvent("UIBtnOnClick", btn_skillItem.name , GetType().ToString());
            Debug.Log($"_______UIBtnOnClick,name:btn_skillItem typeName:{GetType()}");
        });


    }

    public abstract override void OnHide();



}
