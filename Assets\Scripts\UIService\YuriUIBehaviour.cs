using System;
using UnityEngine;

/// <summary>
/// UI行为脚本基类
/// </summary>
public abstract class Yuri<PERSON>Behaviour : MonoBehaviour
{
    protected E_UILayer layer = E_UILayer.Middle;
    public E_UILayer Layer
    {
        get
        {
            return layer;
        }
    }

    /// <summary>
    /// ui在游戏中属于哪一类ui
    /// </summary>
    protected E_UIState uiState = E_UIState.InOrOutOfSession;
    /// <summary>
    /// ui在游戏中属于哪一类ui
    /// 默认为局内外均可展示,如非局内外均可展示 
    /// </summary>
    public E_UIState UIState
    {
        get
        {
            return uiState;
        }
    }

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool IsShow
    {
        get
        {
            return UIService.Instance.GetUIVisibility(this);
        }
    }

    /// <summary>
    /// UI根节点
    /// </summary>
    public Transform RootCanvas
    {
        get
        {
            return gameObject.transform;
        }
    }

    // public void Show(params dynamic[] values)
    // {
    //     if (values == null || values.Length == 0)
    //     {
    //         this.OnShow();
    //     }
    //     var method = this.GetType().GetMethod("OnShow");
    //     System.Reflection.ParameterInfo[] parameters = method.GetParameters();
    //     foreach (var param in parameters)
    //     {
    //         Debug.Log($"{param.Name}: {param.ParameterType}");
    //     }
    //     switch (values.Length)
    //     {
    //         case 1:
    //             OnShow(values[0]);
    //             break;
    //         case 2:
    //             OnShow(values[0], values[1]);
    //             break;
    //         case 3:
    //             OnShow(values[0], values[1], values[2]);
    //             break;
    //         case 4:
    //             OnShow(values[0], values[1], values[2], values[3]);
    //             break;
    //         case 5:
    //             OnShow(values[0], values[1], values[2], values[3], values[4]);
    //             break;
    //         case 6:
    //             OnShow(values[0], values[1], values[2], values[3], values[4], values[5]);
    //             break;
    //         default:
    //             OnShow();
    //             break;
    //     }
    // }

    // public virtual void OnShow() { }
    // public virtual void OnShow<T>(T arg) { }
    // public virtual void OnShow<T1, T2>(T1 arg1, T2 arg2) { }
    // public virtual void OnShow<T1, T2, T3>(T1 arg1, T2 arg2, T3 arg3) { }
    // public virtual void OnShow<T1, T2, T3, T4>(T1 arg1, T2 arg2, T3 arg3, T4 arg4) { }
    // public virtual void OnShow<T1, T2, T3, T4, T5>(T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5) { }
    // public virtual void OnShow<T1, T2, T3, T4, T5, T6>(T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5, T6 arg6) { }

    public virtual void Awake()
    {

    }


    public virtual void Start()
    {

    }

    // 保留原有接口用于框架调用，支持JavaScript风格的多参数
    public virtual void OnShow(params object[] values)
    {
        if (values == null || values.Length == 0)
        {
            OnShow();
            return;
        }

        // 根据参数数量和类型查找对应的重载方法
        Type[] paramTypes = new Type[values.Length];
        object[] args = new object[values.Length];

        for (int i = 0; i < values.Length; i++)
        {
            paramTypes[i] = values[i]?.GetType() ?? typeof(object);
            args[i] = values[i];
        }

        var method = GetType().GetMethod("OnShow",
            System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance,
            null, paramTypes, null);

        if (method != null)
        {
            method.Invoke(this, args);
        }
        else
        {
            // 如果没有找到匹配的重载，调用无参版本
            OnShow();
        }
    }

    // 子类重写的无参版本
    public virtual void OnShow() { }

    /// <summary>
    /// 隐藏UI时将自动调用
    /// </summary>
    public abstract void OnHide();

    /// <summary>
    /// 当调用UIService返还UI时会调用此函数,此时UI不一定被真销毁了，而是进入缓存池
    /// </summary>
    public abstract void OnUIReturn();

    protected void OnDestroy()
    {
        OnUIReturn();
    }

}
