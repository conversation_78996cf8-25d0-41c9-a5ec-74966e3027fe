using EventObjSystem;
using UnityEngine;
using static EventsTable;

namespace SideScrollingScene
{
    /// <summary>
    /// 事件出生点
    /// </summary>
    public class EventSpawnPoint : MonoBehaviour
    {
        [Header("是否随机生成事件 - 默认随机,随机则无视下列参数,直接从事件表中随机读取")]
        public bool IsRandomSpawn = true;

        [Header("事件id ")]
        public int EventId = -1;

        [Header("触发的游戏功能")]
        public FeatureType featureType = FeatureType.Default;

        [Header("事件配置")]
        public string eventCfg;

        [Header("事件对象类型 - 不填为场景默认事件对象类型")]
        public EventObjType EventObjType = EventObjType.Default;

        [Header("如果事件类型为npc,需填入npcId")]
        public int NpcId = -1;

        [Header("是否是自动触发")]
        public bool IsAutoTrigger = false;
    }
}