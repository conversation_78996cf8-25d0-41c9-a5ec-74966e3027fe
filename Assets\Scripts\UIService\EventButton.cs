
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;



/// <summary>
/// 封装的事件button
/// </summary>
public class EventButton
{
    private static int CurrentCheckLayer = 0;
    public static void SetCurrentLayer(int layer)
    {
        CurrentCheckLayer = layer;
    }
    protected GameObject _buttonGo;
    private UIBehaviour _uiBehaviour;
    int _layer = 0;
    public int Layer
    {
        get
        {
            return _layer;
        }
    }

    public GameObject GameObject
    {
        get
        {
            return _buttonGo;
        }
    }

    protected UnityEvent<BaseEventData> _pointerEnter;
    public UnityEvent<BaseEventData> OnPointerEnter
    {
        get
        {
            if (_pointerEnter == null)
            {
                _pointerEnter = addEventListenerEntry(EventTriggerType.PointerEnter);
            }
            return _pointerEnter;
        }
    }

    protected UnityEvent<BaseEventData> _pointerExit;
    public UnityEvent<BaseEventData> OnPointerExit
    {
        get
        {
            if (_pointerExit == null)
            {
                _pointerExit = addEventListenerEntry(EventTriggerType.PointerExit);
            }
            return _pointerExit;
        }
    }

    protected UnityEvent<BaseEventData> _pointerClick;
    public UnityEvent<BaseEventData> OnPointerClick
    {
        get
        {
            if (_pointerClick == null)
            {
                _pointerClick = addEventListenerEntry(EventTriggerType.PointerClick);
            }
            return _pointerClick;
        }
    }

    protected UnityEvent<BaseEventData> _pointerDown;
    public UnityEvent<BaseEventData> OnPointerDown
    {
        get
        {
            if (_pointerDown == null)
            {
                _pointerDown = addEventListenerEntry(EventTriggerType.PointerDown);
            }
            return _pointerDown;
        }
    }

    protected UnityEvent<BaseEventData> _pointerUp;
    public UnityEvent<BaseEventData> OnPointerUp
    {
        get
        {
            if (_pointerUp == null)
            {
                _pointerUp = addEventListenerEntry(EventTriggerType.PointerUp);
            }
            return _pointerUp;
        }
    }

    protected UnityEvent<BaseEventData> _pointerDrag;
    public UnityEvent<BaseEventData> OnPointerDrag
    {
        get
        {
            if (_pointerDrag == null)
            {
                _pointerDrag = addEventListenerEntry(EventTriggerType.Drag);
            }
            return _pointerDrag;
        }
    }

    protected UnityEvent<BaseEventData> _pointerBeginDrag;
    public UnityEvent<BaseEventData> OnPointerBeginDrag
    {
        get
        {
            if (_pointerBeginDrag == null)
            {
                _pointerBeginDrag = addEventListenerEntry(EventTriggerType.BeginDrag);
            }
            return _pointerBeginDrag;
        }
    }

    protected UnityEvent<BaseEventData> _pointerEndDrag;
    public UnityEvent<BaseEventData> OnPointerEndDrag
    {
        get
        {
            if (_pointerEndDrag == null)
            {
                _pointerEndDrag = addEventListenerEntry(EventTriggerType.EndDrag);
            }
            return _pointerEndDrag;
        }
    }

    protected EventTrigger eventTrigger;

    UnityEvent<BaseEventData> addEventListenerEntry(EventTriggerType type)
    {
        EventTrigger.Entry newEntry;
        if (_uiBehaviour)
        {
            newEntry = EventEntryUtil.GetCustomEventListenerEntry(_uiBehaviour, type);
        }
        else
        {
            newEntry = EventEntryUtil.GetCustomEventListenerEntry(_buttonGo, type);
        }
        newEntry.callback.RemoveAllListeners();    //添加的时候无论原来有没有 清一下
        UnityEvent<BaseEventData> action = new();
        newEntry.callback.AddListener((BaseEventData data) =>
        {
            if (CurrentCheckLayer != _layer)
            {
                return;
            }
            action.Invoke(data);
        });

        return action;
    }

    public EventButton(GameObject go, int layer = 0)
    {
        _buttonGo = go;
        SetLayer(layer);
        AddEventTrigger();
    }

    public EventButton(UIBehaviour uiBehaviour, int layer = 0)
    {
        _buttonGo = uiBehaviour.gameObject;
        _uiBehaviour = uiBehaviour;
        SetLayer(layer);
        AddEventTrigger();
    }

    void SetLayer(int layer = 0)
    {
        _layer = layer;
    }

    void AddEventTrigger()
    {
        eventTrigger = _buttonGo.GetComponent<EventTrigger>();
        if (!eventTrigger)
        {
            eventTrigger = _buttonGo.AddComponent<EventTrigger>();
        }

    }



    public void SetEnable(bool isEnable)
    {
        eventTrigger.enabled = isEnable;
    }

    public void SetActive(bool active)
    {
        _buttonGo.SetActive(active);
        SetEnable(active);
    }

    public void Destroy()
    {

    }
}