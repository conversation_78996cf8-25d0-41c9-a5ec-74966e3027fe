using Player;
using SideScrollingScene;
using UnityEngine;
using static EventsTable;

namespace EventObjSystem
{

    public enum E_CardState
    {
        Faraway,
        Closer,
        Front
    }

    public class EventCard : IEventObj
    {

        private EventCardStyle _cardStyleInfo;
        public EventCardStyle CardStyleInfo
        {
            get
            {
                return _cardStyleInfo;
            }
        }

        public float CloserDistance = 5f;
        public float FrontDistance = 2f;

        E_CardState _cardState = E_CardState.Faraway;
        public E_CardState CardState
        {
            get
            {
                return _cardState;
            }
            private set
            {
                if (_cardState == value)
                {
                    return;
                }
                _cardState = value;
                OnCardStateChange();
                Vector3 playerPos = PlayerEntity.Instance.Q_Player.transform.position;
                Vector3 cardPos = transform.position;
                float distance = Vector3.Distance(playerPos, cardPos);
                Debug.Log($"_________cardState{value} {distance}");
            }
        }

        public FlipCard mFlipCard;
        public ShadowMover mShadowMover;
        public GameObject ShadowCircle;

        private bool _isInitialized = false;
        public override void Init()
        {
            if (IsRandomSpawn && !CheckIdValid(EventId))
            {
                gameObject.SetActive(false);
                mFlipCard.ResetFlip();
                mFlipCard.gameObject.SetActive(false);
                mShadowMover.gameObject.SetActive(false);
                ShadowCircle.SetActive(false);
                _isInitialized = false;
                Debug.LogError($"_________EventCard Init failed eventId:{EventId}");
                return;
            }
            mFlipCard.gameObject.SetActive(true);
            _cardStyleInfo = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventCardStyle(styleKey);
            mFlipCard.Init(this);
            mFlipCard.ResetFlip();
            mShadowMover.gameObject.SetActive(true);
            ShadowCircle.SetActive(true);
            gameObject.SetActive(true);
            _isInitialized = true;
        }

        void Update()
        {
            if (CanUpdate())
            {
                Vector3 playerPos = PlayerEntity.Instance.Q_Player.transform.position;
                Vector3 cardPos = transform.position;
                float distance = Vector3.Distance(playerPos, cardPos);
                distance = Mathf.Abs(distance);
                if (distance > CloserDistance)
                {
                    CardState = E_CardState.Faraway;
                }
                else if (distance <= CloserDistance && distance > FrontDistance)
                {
                    CardState = E_CardState.Closer;
                }
                else if (distance <= FrontDistance)
                {
                    CardState = E_CardState.Front;
                }
            }
        }

        bool CanUpdate()
        {
            return PlayerEntity.Instance.Q_Player != null && _isInitialized;
        }

        void OnCardStateChange()
        {
            mFlipCard.OnCardStateChange(CardState);
            switch (CardState)
            {
                case E_CardState.Faraway:
                    mShadowMover.StopConveyorEffect();
                    break;
                case E_CardState.Closer:
                    mShadowMover.StartConveyorEffect();
                    break;
                case E_CardState.Front:
                    mShadowMover.StartConveyorEffect();
                    break;
            }
        }


        void OnDestroy()
        {
            // 清理状态，确保Gizmos不再绘制
            _isInitialized = false;
        }

        void OnDrawGizmos()
        {
            // 只在物体激活且初始化完成时绘制Gizmos
            if (!gameObject.activeInHierarchy || !_isInitialized)
                return;

            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, CloserDistance);
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, FrontDistance);
        }

    }
}