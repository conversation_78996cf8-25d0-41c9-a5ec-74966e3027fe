using Common;
using UnityEngine;

namespace AdventureModule.UI
{
    public class SkillItem : SkillItemGenerated
    {
        static string EmptyPath = "Sprites/basic_ui/advUI_card00";
        static string SelectedPath = "Sprites/basic_ui/advUI_card001";
        static string TempChosenPath = "Sprites/basic_ui/advUI_card01";

        private int _cfgId;
        public int CfgId
        {
            get => _cfgId;
        }

        public override void OnShow()
        {

        }

        public void OnSelected()
        {
            img_skillItem.sprite = ResourceLoader.Instance.Load<Sprite>(SelectedPath);
        }

        public void OnUnSelected()
        {
            img_skillItem.sprite = ResourceLoader.Instance.Load<Sprite>(EmptyPath);
        }

        public void OnClicked()
        {

        }

        public void OnLoadSkill()
        {
            img_skillItem.sprite = ResourceLoader.Instance.Load<Sprite>(TempChosenPath);
        }

        public override void OnHide()
        {

        }

        public override void OnUIReturn()
        {

        }
    }
}