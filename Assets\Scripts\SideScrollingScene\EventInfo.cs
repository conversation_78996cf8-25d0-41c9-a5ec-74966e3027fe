using Cysharp.Threading.Tasks;
using EventObjSystem;
using UnityEngine;
using static EventsTable;

namespace SideScrollingScene
{
    enum EventStat
    {
        Upspawn,
        Initializing,
        Exist,
        Destroy
    }

    class EventInfo
    {
        private EventStat _eventStat;
        public EventStat EventState
        {
            get
            {
                return _eventStat;
            }
        }

        EventSpawnPoint _spawnPoint;
        public Vector3 Position
        {
            get
            {
                return _spawnPoint.transform.position;
            }
        }
        private int _eventId;
        public int EventId
        {
            get
            {
                return _eventId;
            }
        }
        IEventObj _eventObj;
        public IEventObj EventObj
        {
            get
            {
                return _eventObj;
            }
        }

        public EventInfo(EventSpawnPoint point)
        {
            _spawnPoint = point;
            _eventStat = EventStat.Upspawn;
            _eventObj = null;
        }

        public void InitEvent(int eventId)
        {
            _eventStat = EventStat.Exist;
            _eventId = eventId;
            EventObjType type;
            int npcId;
            if (_spawnPoint.IsRandomSpawn)
            {
                EventEntry eventData = ExcelDataMgr.Instance.GetData<EventsTable, EventEntry>(eventId);
                type = eventData.eventObjType;
                npcId = eventData.npcId;
            }
            else
            {
                type = _spawnPoint.EventObjType;
                npcId = _spawnPoint.NpcId;
            }

            switch (type)
            {
                case EventObjType.Npc:
                    EventObjManager.Instance.CreateEventNpc(eventId, npcId).ContinueWith((obj) =>
                    {
                        InitEventObj(obj);
                    });
                    break;
                case EventObjType.Card:
                    EventObjManager.Instance.CreateEventCard(eventId).ContinueWith((obj) =>
                      {
                          InitEventObj(obj);
                      });
                    break;
                default:
                    break;
            }
        }

        void InitEventObj(IEventObj obj)
        {
            _eventStat = EventStat.Exist;
            _eventObj = obj;
            _eventObj.transform.parent = _spawnPoint.transform.parent;
            _eventObj.transform.position = _spawnPoint.transform.position;
            _eventObj.IsAutoTrigger = false;
            _eventObj.gameObject.SetActive(true);
            Debug.Log($"________InitEventObj:{_eventId}");
        }

        public void DestoryEvent()
        {
            _eventStat = EventStat.Destroy;
            if (_eventObj != null)
            {
                EventObjManager.Instance.DestroyEventObj(_eventObj.gameObject);
                _eventObj = null;
            }
            Debug.Log($"________DestroyEventObj:{_eventId}");
        }

        public bool IsByPlayerLeft(Vector3 pos)
        {
            return pos.x > _spawnPoint.transform.position.x;
        }

        public float Distance(Vector3 pos)
        {
            return Mathf.Abs(pos.x - _spawnPoint.transform.position.x);
        }
    }

}