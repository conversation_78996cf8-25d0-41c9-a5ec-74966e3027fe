using Cysharp.Threading.Tasks;
using EventObjSystem;
using UnityEngine;
using static EventsTable;

namespace SideScrollingScene
{
    enum EventStat
    {
        Upspawn,
        Initializing,
        Exist,
        Destroy
    }

    class EventInfo
    {
        private EventStat _eventStat;
        public EventStat EventState
        {
            get
            {
                return _eventStat;
            }
        }

        EventSpawnPoint _spawnPoint;
        public Vector3 Position
        {
            get
            {
                return _spawnPoint.transform.position;
            }
        }
        private int _eventId;
        public int EventId
        {
            get
            {
                return _eventId;
            }
        }
        IEventObj _eventObj;
        public IEventObj EventObj
        {
            get
            {
                return _eventObj;
            }
        }

        public EventInfo(EventSpawnPoint point)
        {
            _spawnPoint = point;
            _eventStat = EventStat.Upspawn;
            _eventObj = null;
        }

        public void InitEvent(int eventId)
        {
            _eventStat = EventStat.Exist;
            _eventId = eventId;
            EventObjType type;
            int npcId;
            if (_spawnPoint.IsRandomSpawn)
            {
                EventEntry eventData = ExcelDataMgr.Instance.GetData<EventsTable, EventEntry>(eventId);
                type = eventData.eventObjType;
                npcId = eventData.npcId;
            }
            else
            {
                type = _spawnPoint.EventObjType;
                npcId = _spawnPoint.NpcId;
            }

            switch (type)
            {
                case EventObjType.Npc:
                    CreateEventNpcAsync(eventId, npcId).Forget();
                    break;
                case EventObjType.Card:
                    CreateEventCardAsync(eventId).Forget();
                    break;
                default:
                    break;
            }
        }

        void InitEventObj(IEventObj obj)
        {
            _eventStat = EventStat.Exist;
            _eventObj = obj;
            _eventObj.transform.parent = _spawnPoint.transform.parent;
            _eventObj.transform.position = _spawnPoint.transform.position;
            _eventObj.IsAutoTrigger = false;
            _eventObj.gameObject.SetActive(true);
            Debug.Log($"________InitEventObj:{_eventId}");
        }

        public void DestoryEvent()
        {
            _eventStat = EventStat.Destroy;
            if (_eventObj != null)
            {
                EventObjManager.Instance.DestroyEventObj(_eventObj.gameObject);
                _eventObj = null;
            }
            Debug.Log($"________DestroyEventObj:{_eventId}");
        }

        public bool IsByPlayerLeft(Vector3 pos)
        {
            return pos.x > _spawnPoint.transform.position.x;
        }

        public float Distance(Vector3 pos)
        {
            return Mathf.Abs(pos.x - _spawnPoint.transform.position.x);
        }

        /// <summary>
        /// 异步创建事件NPC，带异常处理
        /// </summary>
        private async UniTaskVoid CreateEventNpcAsync(int eventId, int npcId)
        {
            try
            {
                Debug.Log($"开始创建事件NPC - EventId: {eventId}, NpcId: {npcId}");

                var obj = await EventObjManager.Instance.CreateEventNpc(eventId, npcId);
                if (obj != null)
                {
                    Debug.Log($"事件NPC创建成功 - EventId: {eventId}, NpcId: {npcId}");
                    InitEventObj(obj);
                }
                else
                {
                    Debug.LogError($"创建事件NPC失败! 返回对象为null - EventId: {eventId}, NpcId: {npcId}");
                    _eventStat = EventStat.Destroy;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"创建事件NPC时发生异常! EventId: {eventId}, NpcId: {npcId}");
                Debug.LogError($"异常详情: {ex.Message}");
                Debug.LogError($"堆栈跟踪: {ex.StackTrace}");
                _eventStat = EventStat.Destroy;
            }
        }

        /// <summary>
        /// 异步创建事件卡牌，带异常处理
        /// </summary>
        private async UniTaskVoid CreateEventCardAsync(int eventId)
        {
            try
            {
                var obj = await EventObjManager.Instance.CreateEventCard(eventId);
                if (obj != null)
                {
                    InitEventObj(obj);
                }
                else
                {
                    Debug.LogError($"创建事件卡牌失败! EventId: {eventId}");
                    _eventStat = EventStat.Destroy;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"创建事件卡牌时发生异常! EventId: {eventId}, 异常: {ex.Message}");
                _eventStat = EventStat.Destroy;
            }
        }
    }

}