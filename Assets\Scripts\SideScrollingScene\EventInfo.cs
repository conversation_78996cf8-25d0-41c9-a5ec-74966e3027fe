using Cysharp.Threading.Tasks;
using EventObjSystem;
using UnityEngine;
using static EventsTable;

namespace SideScrollingScene
{
    enum EventStat
    {
        Upspawn,
        Initializing,
        Exist,
        Destroy
    }

    class EventInfo
    {
        private EventStat _eventStat;
        public EventStat EventState
        {
            get
            {
                return _eventStat;
            }
        }

        EventSpawnPoint _spawnPoint;
        public Vector3 Position
        {
            get
            {
                return _spawnPoint.transform.position;
            }
        }
        private int _eventId;
        public int EventId
        {
            get
            {
                return _eventId;
            }
        }
        IEventObj _eventObj;
        public IEventObj EventObj
        {
            get
            {
                return _eventObj;
            }
        }

        public EventInfo(EventSpawnPoint point)
        {
            _spawnPoint = point;
            _eventStat = EventStat.Upspawn;
            _eventObj = null;
        }

        public void InitEvent(int eventId)
        {
            _eventId = eventId;
            EventObjType type;
            int npcId;
            bool shouldSpawnByPoint = false;
            EventEntry eventData = ExcelDataMgr.Instance.GetData<EventsTable, EventEntry>(eventId);
            if (eventData != null)
            {
                type = eventData.eventObjType;
                npcId = eventData.npcId;
            }
            else
            {
                if (!_spawnPoint.IsRandomSpawn)
                {
                    shouldSpawnByPoint = true;
                    type = _spawnPoint.EventObjType;
                    npcId = _spawnPoint.NpcId;
                }
                else
                {
                    Debug.LogError($"NotRandomSpawn and invalid eventId:{eventId} eventData is null");
                    return;
                }
            }
            _eventStat = EventStat.Exist;
            EventSpawnPoint point = shouldSpawnByPoint ? _spawnPoint : null;
            switch (type)
            {
                case EventObjType.Npc:
                    CreateEventNpcAsync(eventId, npcId, point).Forget();
                    break;
                case EventObjType.Card:
                    CreateEventCardAsync(eventId, point).Forget();
                    break;
                default:
                    break;
            }
        }

        void InitEventObj(IEventObj obj)
        {
            _eventStat = EventStat.Exist;
            _eventObj = obj;
            _eventObj.transform.parent = _spawnPoint.transform.parent;
            _eventObj.transform.position = _spawnPoint.transform.position;
            _eventObj.gameObject.SetActive(true);
            Debug.Log($"________InitEventObj:{_eventId}");
        }

        public void DestoryEvent()
        {
            _eventStat = EventStat.Destroy;
            if (_eventObj != null)
            {
                _eventObj.Destory();
               
                _eventObj = null;
            }
            Debug.Log($"________DestroyEventObj:{_eventId}");
        }

        public bool IsByPlayerLeft(Vector3 pos)
        {
            return pos.x > _spawnPoint.transform.position.x;
        }

        public float Distance(Vector3 pos)
        {
            return Mathf.Abs(pos.x - _spawnPoint.transform.position.x);
        }

        /// <summary>
        /// 异步创建事件NPC，带异常处理
        /// </summary>
        private async UniTaskVoid CreateEventNpcAsync(int eventId, int npcId, EventSpawnPoint point = null)
        {
            try
            {
                var obj = await EventObjManager.Instance.CreateEventNpc(eventId, npcId, point);
                if (obj != null)
                {
                    InitEventObj(obj);
                }
                else
                {
                    Debug.LogError($"创建事件NPC失败! EventId: {eventId}, NpcId: {npcId}");
                    _eventStat = EventStat.Destroy;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"创建事件NPC时发生异常! EventId: {eventId}, NpcId: {npcId}, 异常: {ex.Message}");
                _eventStat = EventStat.Destroy;
            }
        }

        /// <summary>
        /// 异步创建事件卡牌，带异常处理
        /// </summary>
        private async UniTaskVoid CreateEventCardAsync(int eventId, EventSpawnPoint point = null)
        {
            try
            {
                var obj = await EventObjManager.Instance.CreateEventCard(eventId, point);
                if (obj != null)
                {
                    InitEventObj(obj);
                }
                else
                {
                    Debug.LogError($"创建事件卡牌失败! EventId: {eventId}");
                    _eventStat = EventStat.Destroy;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"创建事件卡牌时发生异常! EventId: {eventId}, 异常: {ex.Message}");
                _eventStat = EventStat.Destroy;
            }
        }
    }

}