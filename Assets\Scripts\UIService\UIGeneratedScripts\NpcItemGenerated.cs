using UnityEngine;
using UnityEngine.EventSystems;

public abstract class NpcItemGenerated : Yuri<PERSON><PERSON>ehaviour
{
    // 节点变量
    protected UnityEngine.UI.Image img_node_NotSelect;
    protected Transform node_NotSelect;
    protected UnityEngine.UI.Image img_node_Chosen;
    protected Transform node_Chosen;
    protected UnityEngine.UI.Image img_npcIcon;
    protected UnityEngine.UI.Image img_npcbar_Def;
    protected TMPro.TextMeshProUGUI tm_num_Def;
    protected UnityEngine.UI.Image img_npcbar_Hp;
    protected TMPro.TextMeshProUGUI tm_num_Hp;
    protected Transform node_Skill;


    public override void Awake()
    {
        // 初始化节点
        img_node_NotSelect = transform.Find("node_NotSelect").gameObject.GetComponent<UnityEngine.UI.Image>();
        node_NotSelect = transform.Find("node_NotSelect");
        img_node_Chosen = transform.Find("node_Chosen").gameObject.GetComponent<UnityEngine.UI.Image>();
        node_Chosen = transform.Find("node_Chosen");
        img_npcIcon = transform.Find("node_Chosen/npcIcon").gameObject.GetComponent<UnityEngine.UI.Image>();
        img_npcbar_Def = transform.Find("node_Chosen/npcbar_Def").gameObject.GetComponent<UnityEngine.UI.Image>();
        tm_num_Def = transform.Find("node_Chosen/npcbar_Def/num_Def").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        img_npcbar_Hp = transform.Find("node_Chosen/npcbar_Hp").gameObject.GetComponent<UnityEngine.UI.Image>();
        tm_num_Hp = transform.Find("node_Chosen/npcbar_Hp/num_Hp").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        node_Skill = transform.Find("node_Skill");


        // 给按钮添加一个初始点击事件


    }

    public abstract override void OnHide();



}
