using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Player;
using UnityEngine;
using Random = UnityEngine.Random;

namespace SideScrollingScene
{
    /// <summary>
    /// 流式加载管理类
    /// </summary>
    public class ChunkStreamingManager : MonoBehaviour
    {
        public List<Chunk> SceneChunks = new();

        [Header("地图最左空气墙")]
        public Transform EndAirWall_Left;

        [Header("地图最右空气墙")]
        public Transform EndAirWall_Right;

        [Header("玩家是否随机生成 - 若false或没有随机点位,则默认生成在默认点位,")]
        public bool IsPlayerRandomSpawn = true;

        [Header("玩家默认出生点")]
        public Transform PlayerDefaultSpawnPoint;

        protected Q_PlayerComp player;

        protected List<Chunk> _activeChunks = new();

        private float GC_CountDown = 0;
        /// <summary>
        /// 每1分钟解除一次内存占用
        /// </summary>
        private float GC_Interval = 60f;

        private float ActiveDistance = 40f;
        private float UnloadDistance = 50f;
        bool canUpdate = false;

        protected virtual void Awake()
        {
            canUpdate = false;
            DeactiveAllChunks();
        }

        void Start()
        {
            //初始化玩家
            PlayerEntity.Instance.InitQPlayer().ContinueWith((qPlayer) =>
            {
                player = qPlayer.GetComponent<Q_PlayerComp>();
                InitQplayer();
            });
        }

        void Update()
        {
            if (!canUpdate)
            {
                return;
            }

            if (player.IsMoving)
            {
                //玩家移动时进行检测，不移动不进行
                CheckChunks();
            }
            OnUpdate();
        }

        protected virtual void OnUpdate()
        {

        }


        public void CheckChunks()
        {

            foreach (var chunk in SceneChunks)
            {

                bool isLoad = Vector2.Distance(player.gameObject.transform.position, chunk.transform.position) < ActiveDistance;
                if (isLoad)
                {
                    if (!_activeChunks.Contains(chunk))
                    {
                        chunk.Load();
                        _activeChunks.Add(chunk);
                    }
                }

                bool isUnLoad = Vector2.Distance(player.gameObject.transform.position, chunk.transform.position) > UnloadDistance;
                if (isUnLoad && _activeChunks.Contains(chunk))
                {
                    chunk.UnLoad();
                    _activeChunks.Remove(chunk);
                }
            }

            FreeAssets();
        }

        /// <summary>
        /// 释放资源 - 不需要 解除占用就行了
        /// </summary>
        public void FreeAssets()
        {
            if (GC_CountDown < GC_Interval)
            {
                GC_CountDown += Time.deltaTime;
                return;
            }
            GC_CountDown = 0;
            Resources.UnloadUnusedAssets();
        }

        void InitQplayer()
        {
            PlayerSpawn();
            CheckChunks();
            OnAfterPlayerInitBeforePlayerActive();
            //设置玩家行走范围
            player.SetPlayerMoveLimit(new(EndAirWall_Left.position.x, EndAirWall_Right.position.x));
            player.SetActive(true);
            canUpdate = true;
        }

        protected virtual void OnAfterPlayerInitBeforePlayerActive()
        {

        }

        /// <summary>
        /// 玩家出生逻辑
        /// </summary>
        void PlayerSpawn()
        {
            if (IsPlayerRandomSpawn)
            {
                List<Chunk> chunksWithSpawnPoints = SceneChunks.FindAll((chunk) => chunk.PlayerSpawnPoints.Count > 0);
                if (chunksWithSpawnPoints.Count > 0)
                {
                    int randIdx = Random.Range(0, chunksWithSpawnPoints.Count);
                    int idx = Random.Range(0, chunksWithSpawnPoints[randIdx].PlayerSpawnPoints.Count);
                    player.SetQPlayerPosition(chunksWithSpawnPoints[randIdx].PlayerSpawnPoints[idx].position);
                    return;
                }
            }
            player.SetQPlayerPosition(PlayerDefaultSpawnPoint.position);
        }


        void DeactiveAllChunks()
        {
            foreach (Chunk chunk in SceneChunks)
            {
                _activeChunks.Remove(chunk);
                chunk.UnLoad();
            }
        }
    }
}
