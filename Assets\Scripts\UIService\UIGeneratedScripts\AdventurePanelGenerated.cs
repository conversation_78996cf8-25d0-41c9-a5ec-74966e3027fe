using UnityEngine;
using UnityEngine.EventSystems;

public abstract class AdventurePanelGenerated : <PERSON><PERSON><PERSON>eh<PERSON><PERSON>
{
    // 节点变量
    protected UnityEngine.UI.Image img_symbol_1;
    protected TMPro.TextMeshProUGUI tm_mapItemName;
    protected UnityEngine.UI.Image img_symbol_2;


    public override void Awake()
    {
        // 初始化节点
        img_symbol_1 = transform.Find("NamePanel/symbol_1").gameObject.GetComponent<UnityEngine.UI.Image>();
        tm_mapItemName = transform.Find("NamePanel/mapItemName").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
        img_symbol_2 = transform.Find("NamePanel/symbol_2").gameObject.GetComponent<UnityEngine.UI.Image>();


        // 给按钮添加一个初始点击事件


    }

    public abstract override void OnShow(params dynamic[] values);
    public abstract override void OnHide();



}
