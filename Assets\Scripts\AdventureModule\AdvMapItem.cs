using UnityEngine;
using UnityEngine.UI;

namespace AdventureModule
{
    public class AdvMapItem : MonoBehaviour
    {

        [Header("配置id")]
        public int CfgId = -1;

        protected Image img_Map;
        protected TMPro.TextMeshProUGUI tm_Tip;
        protected Button btn_Checker;

        public void Awake()
        {
            img_Map = transform.gameObject.GetComponent<Image>();
            tm_Tip = transform.Find("TipText").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
            btn_Checker = transform.gameObject.GetComponent<Button>();
            btn_Checker.onClick.AddListener(() =>
            {

            });
            
        }

        void OnValidate()
        {

        }


    }
}
