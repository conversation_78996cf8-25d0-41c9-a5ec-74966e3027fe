using Common;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace AdventureModule
{
    public enum MapItemStat
    {
        Locked,
        Unlocked
    }

    public class AdvMapItem : MonoBehaviour
    {

        [Header("配置id")]
        public int CfgId = -1;

        protected Image img_Map;
        protected TMPro.TextMeshProUGUI tm_Tip;
        EventButton eventButton;

        MapItem mapItemCfg;
        string pathPrefix = "";
        AdventurePanel panel;
        public void Awake()
        {
            mapItemCfg = ExcelDataMgr.Instance.GetTable<AdventureMap>().GetMapItem(CfgId);
            pathPrefix = $"{ResourcePathConst.AdvMapItem}{mapItemCfg.uiPathPrefix}";
            panel = UIService.Instance.GetUI<AdventurePanel>();
            if (mapItemCfg == null)
            {
                Debug.LogError($"AdvMapItem 配置不存在,请检查CfgId:{CfgId}");
                return;
            }
            img_Map = transform.gameObject.GetComponent<Image>();
            tm_Tip = transform.Find("TipText").gameObject.GetComponent<TMPro.TextMeshProUGUI>();
            tm_Tip.text = "";

            eventButton = new EventButton(img_Map);
            img_Map.sprite = ResourceLoader.Instance.Load<Sprite>($"{pathPrefix}01");
            eventButton.OnPointerEnter.AddListener((BaseEventData data) =>
            {
                img_Map.sprite = ResourceLoader.Instance.Load<Sprite>($"{pathPrefix}02");
                panel.SetName(mapItemCfg.sceneName);
            });

            eventButton.OnPointerExit.AddListener((BaseEventData data) =>
            {
                img_Map.sprite = ResourceLoader.Instance.Load<Sprite>($"{pathPrefix}01");
                panel.SetName("");
            });

            eventButton.OnPointerClick.AddListener((BaseEventData data) =>
            {
                //TODO 场景切换
            });
        }


    }
}
