using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;


/// <summary>
/// 新版UI多重滚动器，支持对象池和选择功能
/// </summary>
/// <typeparam name="T">实现IItemRender接口的Item类型</typeparam>
public class NewUIMultiScroller<T> where T : class, IItemRender
{
    private int _index = -1;

    /// <summary>滚动框</summary>
    private ScrollRect _scrollRect;

    /// <summary>滚动盒子内容</summary>
    private RectTransform _scrollContent;

    private List<T> _itemArr = new List<T>();

    /// <summary>每行显示数量</summary>
    private int _maxPerLine = 3;

    /// <summary>距离左侧和上册的起始距离</summary>
    private float _leftSpace = 30f;
    private float _topSpace = 30f;

    /// <summary>item宽度</summary>
    private float _cellWidth = 500f;

    /// <summary>item高度</summary>
    private float _cellHeight = 100f;

    /// <summary>行间距X</summary>
    private float _spacingX = 40f;

    /// <summary>行间距Y</summary>
    private float _spacingY = 20f;

    /// <summary>默认加载行数，一般比可显示行数大2~3行</summary>
    private int _viewLine = 6;

    /// <summary>item类型</summary>
    private System.Type _itemPrefabType;

    /// <summary>数据数组</summary>
    private object[] _dataArray;

    /// <summary>选中的索引</summary>
    public List<int> SelectIndexs { get; private set; } = new List<int>();

    /// <summary>自动大小</summary>
    private bool _autoSize = false;

    /// <summary>滚动方向</summary>
    public enum ScrollDirection
    {
        Horizontal,
        Vertical
    }

    private ScrollDirection _scrollDirection;

    /// <summary>调用InitData第一次初始化时的回调</summary>
    public event Action<int, T> InitCallback;

    /// <summary>每个Item刷新时的回调</summary>
    public event Action<int, T> ItemCallback;

    /// <summary>
    /// 循环列表构造函数
    /// </summary>
    /// <param name="scrollRect">ScrollRect对象ui的引用</param>
    /// <param name="scrollContent">ScrollRect下的节点的引用</param>
    /// <param name="itemPrefabType">ScrollRectItem预制体类型</param>
    /// <param name="maxPerLine">每行显示的数量</param>
    /// <param name="leftSpace">左边界间距</param>
    /// <param name="topSpace">上边界间距</param>
    /// <param name="viewCount">ScrollRect的默认加载行数</param>
    /// <param name="spacingX">ScrollRect的行间距X</param>
    /// <param name="spacingY">ScrollRect的行间距Y</param>
    /// <param name="autoSize">item自动大小</param>
    public NewUIMultiScroller(ScrollRect scrollRect, RectTransform scrollContent, System.Type itemPrefabType,
        int maxPerLine, float leftSpace = 30f, float topSpace = 30f, int viewCount = 5,
        float spacingX = 40f, float spacingY = 20f, bool autoSize = false)
    {
        _scrollRect = scrollRect;
        _scrollContent = scrollContent;
        _autoSize = autoSize;
        _dataArray = new object[0];

        Init(itemPrefabType, maxPerLine, leftSpace, topSpace, viewCount, spacingX, spacingY, autoSize);
    }

    /// <summary>
    /// 初始化滚动器
    /// </summary>
    /// <param name="itemPrefabType">item类型</param>
    /// <param name="maxPerLine">每行显示的数量</param>
    /// <param name="leftSpace">左边距</param>
    /// <param name="topSpace">上边距</param>
    /// <param name="viewCount">显示列数</param>
    /// <param name="spacingX">item之间的x间距</param>
    /// <param name="spacingY">item之间的y间距</param>
    /// <param name="autoSize">item自动大小</param>
    public void Init(System.Type itemPrefabType, int maxPerLine, float leftSpace = 30f, float topSpace = 30f,
        int viewCount = 5, float spacingX = 40f, float spacingY = 20f, bool autoSize = false)
    {
        _autoSize = autoSize;

        // 清理现有的Item
        if (_itemPrefabType != null)
        {
            RecycleAllItems();
        }

        // 创建临时Item来获取尺寸
        GameObject tempObj = new GameObject("TempItem");
        T tempItem = tempObj.AddComponent(itemPrefabType) as T;
        RectTransform tempRect = tempObj.GetComponent<RectTransform>() ?? tempObj.AddComponent<RectTransform>();

        _cellWidth = tempRect.sizeDelta.x > 0 ? tempRect.sizeDelta.x : 150f;
        _cellHeight = tempRect.sizeDelta.y > 0 ? tempRect.sizeDelta.y : 150f;

        UnityEngine.Object.DestroyImmediate(tempObj);

        // 自动大小计算
        if (_autoSize)
        {
            _scrollDirection = _scrollRect.horizontal ? ScrollDirection.Horizontal : ScrollDirection.Vertical;

            switch (_scrollDirection)
            {
                case ScrollDirection.Horizontal:
                    Debug.Log("横向布局暂不支持item自动大小");
                    break;
                case ScrollDirection.Vertical:
                    Rect scrollRectSize = _scrollRect.GetComponent<RectTransform>().rect;
                    float itemX = (scrollRectSize.width - ((maxPerLine - 1) * spacingX + leftSpace * 2)) / maxPerLine;
                    float scale = itemX / _cellWidth;
                    _cellWidth = itemX;
                    _cellHeight = _cellHeight * scale;
                    break;
            }
        }

        SelectIndexs.Clear();
        _itemPrefabType = itemPrefabType;
        _leftSpace = leftSpace;
        _topSpace = topSpace;

        // 自动计算viewCount
        _viewLine = CalculationItemRowNum(new Vector2(_cellWidth, _cellHeight), _scrollRect);
        _spacingX = spacingX;
        _spacingY = spacingY;
        _maxPerLine = maxPerLine;

        // 添加滚动监听
        _scrollRect.onValueChanged.AddListener(OnValueChange);
    }

    /// <summary>
    /// 计算Item行数
    /// </summary>
    private int CalculationItemRowNum(Vector2 itemSize, ScrollRect scrollRect)
    {
        Rect scrollRectSize = scrollRect.GetComponent<RectTransform>().rect;

        if (_scrollRect.horizontal)
        {
            return Mathf.FloorToInt((scrollRectSize.width - 20) / (itemSize.x + 10)) + 2;
        }
        else
        {
            return Mathf.FloorToInt((scrollRectSize.height - 20) / (itemSize.y + 10)) + 2;
        }
    }

    /// <summary>
    /// 设置数据
    /// </summary>
    /// <param name="val">数据数组</param>
    public void SetData(object[] val)
    {
        _dataArray = val;
        UpdateTotalSize();
        _index = -1;
        ResetScrollPos();

        if (_itemArr != null)
        {
            for (int i = _itemArr.Count - 1; i >= 0; i--)
            {
                T item = _itemArr[i];
                _itemArr.RemoveAt(i);
                ReturnItemToPool(item);
                item.UIObject.SetActive(false);
            }
            OnValueChange(Vector2.zero);
        }
    }

    /// <summary>
    /// 异步分帧设置数据
    /// </summary>
    /// <param name="val">数据数组</param>
    /// <returns>协程</returns>
    public IEnumerator AsyncSetData(object[] val)
    {
        _dataArray = val;
        UpdateTotalSize();
        _index = -1;
        ResetScrollPos();

        if (_itemArr != null)
        {
            for (int i = _itemArr.Count - 1; i >= 0; i--)
            {
                T item = _itemArr[i];
                _itemArr.RemoveAt(i);
                ReturnItemToPool(item);
                item.UIObject.SetActive(false);

                if ((i + 1) % 10 == 0)
                    yield return null; // 每10个Item处理后等待一帧
            }
            OnValueChange(Vector2.zero);
        }
    }

    /// <summary>
    /// 滚动值变化处理
    /// </summary>
    private void OnValueChange(Vector2 scrollValue)
    {
        if (_itemArr == null || _dataArray == null || _dataArray.Length == 0)
            return;

        int index = GetPosIndex();
        if (index < 0 && _index > 0)
        {
            index = 0;
        }

        if (_index != index && index > -1)
        {
            _index = index;

            // 移除不在视野内的Item
            for (int i = _itemArr.Count - 1; i >= 0; i--)
            {
                T item = _itemArr[i];
                if (item.ScrollIndex < index * _maxPerLine || item.ScrollIndex >= (index + _viewLine) * _maxPerLine)
                {
                    _itemArr.RemoveAt(i);
                    ReturnItemToPool(item);
                    item.UIObject.SetActive(false);
                }
            }

            // 创建需要显示的Item
            for (int i = index * _maxPerLine; i < (index + _viewLine) * _maxPerLine; i++)
            {
                if (i < 0) continue;
                if (i > _dataArray.Length - 1) continue;

                bool isExist = false;
                foreach (T item in _itemArr)
                {
                    if (item.ScrollIndex == i)
                    {
                        isExist = true;
                        break;
                    }
                }

                if (!isExist)
                {
                    CreateItem(i);
                }
            }
        }
    }

    /// <summary>
    /// 根据索引号获取当前item的位置
    /// </summary>
    /// <param name="i">索引</param>
    /// <returns>返回位置</returns>
    private Vector2 GetPosition(int i)
    {
        int xpos = i % _maxPerLine;
        int ypos = i / _maxPerLine;

        switch (_scrollDirection)
        {
            case ScrollDirection.Horizontal:
                return new Vector2((_cellWidth + _spacingX) * ypos + _leftSpace,
                                 (_cellHeight + _spacingY) * xpos + _topSpace);
            case ScrollDirection.Vertical:
                return new Vector2((_cellWidth + _spacingX) * xpos + _leftSpace,
                                 (_cellHeight + _spacingY) * ypos + _topSpace);
            default:
                return Vector2.zero;
        }
    }

    /// <summary>
    /// 销毁滚动器
    /// </summary>
    public void Destroy()
    {
        RecycleAllItems();
        // 这里可以添加对象池的销毁逻辑
    }

    /// <summary>
    /// 获取Item数量
    /// </summary>
    /// <returns>Item数量</returns>
    public int GetItemCount()
    {
        return _maxPerLine * _viewLine;
    }

    /// <summary>
    /// 设置Item的索引和位置
    /// </summary>
    /// <param name="item">item对象</param>
    /// <param name="index">索引</param>
    private void SetItemIndex(T item, int index)
    {
        item.ScrollIndex = index;
        RectTransform rectTransform = item.UIObject.GetComponent<RectTransform>();
        rectTransform.anchoredPosition = GetPosition(index);
    }

    /// <summary>
    /// 创建Item对象
    /// </summary>
    /// <param name="index">索引</param>
    private void CreateItem(int index)
    {
        T itemBase = GetItemFromPool();

        // 设置父级和基本属性
        itemBase.UIObject.transform.SetParent(_scrollContent, false);
        itemBase.UIObject.SetActive(true);

        if (_cellWidth > 0 && _cellHeight > 0)
        {
            RectTransform rectTransform = itemBase.UIObject.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(_cellWidth, _cellHeight);
        }

        InitCallback?.Invoke(index, itemBase);
        SetItemIndex(itemBase, index);
        ItemCallback?.Invoke(index, itemBase);

        _itemArr.Add(itemBase);

        // 设置选中状态（如果Item支持）
        if (itemBase is ISelectableItem selectableItem)
        {
            selectableItem.SetSelected(SelectIndexs.Contains(index));
        }
    }

    /// <summary>
    /// 获取最上位置的索引
    /// </summary>
    /// <returns>返回索引</returns>
    private int GetPosIndex()
    {
        Vector2 pos = _scrollContent.anchoredPosition;

        switch (_scrollDirection)
        {
            case ScrollDirection.Horizontal:
                return Mathf.FloorToInt(pos.x / -(_cellWidth + _spacingX));
            case ScrollDirection.Vertical:
                float ret = pos.y / -(_cellHeight + _spacingY);
                return Mathf.FloorToInt(ret);
            default:
                return 0;
        }
    }

    /// <summary>
    /// 根据总数量行列来计算content的真正宽度或者高度
    /// </summary>
    private void UpdateTotalSize()
    {
        if (_dataArray == null) return;

        switch (_scrollDirection)
        {
            case ScrollDirection.Horizontal:
                float width = _cellWidth * _dataArray.Length + _spacingX * (_dataArray.Length - 1);
                float height = _scrollContent.sizeDelta.y;
                _scrollContent.sizeDelta = new Vector2(width, height);
                break;
            case ScrollDirection.Vertical:
                int lineCount = Mathf.CeilToInt((float)_dataArray.Length / _maxPerLine);
                _scrollContent.sizeDelta = new Vector2(_scrollContent.sizeDelta.x,
                    _cellHeight * lineCount + _spacingY * (lineCount - 1) + _topSpace + 140);
                break;
        }
    }

    /// <summary>
    /// 重置滚动位置到顶部
    /// </summary>
    private void ResetScrollPos()
    {
        _scrollContent.anchoredPosition = Vector2.zero;
        _scrollRect.normalizedPosition = Vector2.one;
    }

    /// <summary>
    /// 选择对应索引的item
    /// </summary>
    /// <param name="index">索引</param>
    /// <param name="isSingle">是否单选，默认是</param>
    /// <param name="isLocate">单选定位，只有单选才会定位，默认否</param>
    public void SelectItem(int index, bool isSingle = true, bool isLocate = false)
    {
        if (isSingle)
        {
            SelectIndexs.Clear();
            if (isLocate)
                LocateTo(index);
        }

        SelectIndexs.Add(index);

        // 更新所有可见Item的选中状态
        foreach (T itemBase in _itemArr)
        {
            if (itemBase is ISelectableItem selectableItem)
            {
                selectableItem.SetSelected(SelectIndexs.Contains(itemBase.ScrollIndex));
            }
        }
    }

    /// <summary>
    /// 定位到指定item
    /// </summary>
    /// <param name="index">索引</param>
    public void LocateTo(int index)
    {
        Vector2 targetPos = GetPosition(index);
        _scrollRect.content.anchoredPosition = new Vector2(_scrollRect.content.anchoredPosition.x, -targetPos.y);

        // 延迟触发更新
        if (_scrollRect.gameObject.activeInHierarchy)
        {
            _scrollRect.StartCoroutine(DelayedValueChange());
        }
    }

    private IEnumerator DelayedValueChange()
    {
        yield return new WaitForEndOfFrame();
        OnValueChange(Vector2.zero);
    }

    /// <summary>
    /// 获取当前选中的item
    /// </summary>
    /// <returns>选中的Item列表</returns>
    public List<T> GetCurrentItems()
    {
        List<T> curItems = new List<T>();
        foreach (T itemBase in _itemArr)
        {
            if (SelectIndexs.Contains(itemBase.ScrollIndex))
            {
                curItems.Add(itemBase);
            }
        }
        return curItems;
    }

    #region 对象池相关方法（简化实现）

    /// <summary>
    /// 从对象池获取Item
    /// </summary>
    private T GetItemFromPool()
    {
        // 简化的对象池实现，实际项目中可以使用更复杂的对象池
        GameObject itemObj = new GameObject($"ScrollItem");
        T item = itemObj.AddComponent(_itemPrefabType) as T;
        return item;
    }

    /// <summary>
    /// 将Item返回对象池
    /// </summary>
    private void ReturnItemToPool(T item)
    {
        // 简化实现，直接销毁
        if (item?.UIObject != null)
        {
            UnityEngine.Object.Destroy(item.UIObject);
        }
    }

    /// <summary>
    /// 回收所有Item
    /// </summary>
    private void RecycleAllItems()
    {
        foreach (T item in _itemArr)
        {
            ReturnItemToPool(item);
        }
        _itemArr.Clear();
    }

    #endregion
}

/// <summary>
/// 可选择的Item接口
/// </summary>
public interface ISelectableItem
{
    void SetSelected(bool isSelected);
}

