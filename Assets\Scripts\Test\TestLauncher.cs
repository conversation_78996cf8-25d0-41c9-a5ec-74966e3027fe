using AdventureModule;
using UnityEngine;

public class TestLauncher : MonoBehaviour
{
    public void Start()
    {

    }

    public void Update()
    {
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            UIService.Instance.Show<AdventurePanel>();
        }

        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            UIService.Instance.Hide<AdventurePanel>();
        }
    }
}