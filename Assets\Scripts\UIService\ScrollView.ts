import { Debug } from "../../../ares/utils/Debug";

import { ObjectPoolServices } from "./ObjectPoolServices";
import { IItemRender } from "./SuperScrollView";

export class NewUIMultiScroller<T extends IItemRender> {
    _index = -1;
    /**滚动框 */
    _sBox: mw.ScrollBox;
    /**滚动盒子内容 */
    _scrollContent: mw.Canvas;
    _itemArr: Array<T> = new Array();
    /**每行显示数量 */
    _maxPerLine = 3;
    // 距离左侧和上册的起始距离
    _leftSpace = 30;
    _topSpace = 30;
    /**item宽度 */
    _cellWidth = 500;
    /**item高度 */
    _cellHeight = 100;
    // 行间距X
    _spacingX = 40;
    // 行间距Y
    _spacingY = 20;
    //默认加载行数，一般比可显示行数大2~3行
    _viewLine = 6;
    /**item类 */
    _itemPrefab;
    /**数据数组 */
    _dataArray: Array<any>

    /**选中的索引 */
    selectIndexs: number[] = []
    /**自动大小 */
    _autoSize: boolean = false

    /**
    * 循环列表构造函数
    * @param sbox       ScrollBox对象ui的引用
    * @param sr         ScrollBox下的节点的引用
    * @param prefab     ScrollBoxItem预制体
    * @param maxPerLine 每行显示的数量
    * @param leftSpace  左边界间距
    * @param topSpace   上边界间距 
    * @param viewCount  ScrollBox的默认加载行数
    * @param spacingX   ScrollBox的行间距X
    * @param spacingY   ScrollBox的行间距Y
    * @param autoSize   item自动大小
    */
    constructor(sbox, sr, preafab, maxPerLine, leftSpace = 30, topSpace = 30, viewCount = 5, spacingX = 40, spacingY = 20, autoSize = false) {
        this._sBox = sbox;
        this._scrollContent = sr;
        this._autoSize = autoSize;
        this._dataArray = [];
        this.init(preafab, maxPerLine, leftSpace, topSpace, viewCount, spacingX, spacingY, autoSize)
    }

    /**
     *  
     * @param preafab item类
     * @param maxPerLine 每行显示的数量
     * @param leftSpace 左边距
     * @param topSpace 上边距
     * @param viewCount 显示列数
     * @param spacingX item之间的x间距
     * @param spacingY item之间的y间距
    * @param autoSize   item自动大小
     */
    init(preafab, maxPerLine, leftSpace = 30, topSpace = 30, viewCount = 5, spacingX = 40, spacingY = 20, autoSize = false) {
        this._autoSize = autoSize

        if (this._itemPrefab)
            ObjectPoolServices.recycleAllPanel(this._itemPrefab, this._itemArr)
        //根据实际的UI对象size
        let item = ObjectPoolServices.createPanel(preafab)
        this._cellWidth = item.uiObject.size.x;
        this._cellHeight = item.uiObject.size.y
        ObjectPoolServices.recycleAllPanel(preafab, [item])
        let sboxSize = this._sBox.size
        if (this._autoSize) {
            switch (this._sBox.orientation) {
                case mw.Orientation.OrientHorizontal:
                    Debug.log(`横向布局暂不支持item自动大小`);

                    break

                case mw.Orientation.OrientVertical:
                    let itemX = 0
                    itemX = (sboxSize.x - ((maxPerLine - 1) * spacingX + leftSpace * 2)) / maxPerLine
                    let k = itemX / this._cellWidth
                    this._cellWidth = itemX
                    this._cellHeight = this._cellHeight * k
                    break
            }
        }

        this.selectIndexs.length = 0
        this._itemPrefab = preafab;
        this._leftSpace = leftSpace;
        this._topSpace = topSpace;
        // this._cellWidth = cellWidth;
        // this._cellHeight = cellHeight;
        // this._viewLine = viewCount;
        //自动计算viewCount不用了
        this._viewLine = this.calculationItemRowNum(new mw.Vector2(this._cellWidth, this._cellHeight), this._sBox,)
        this._spacingX = spacingX;
        this._spacingY = spacingY;
        this._maxPerLine = maxPerLine;
        this._sBox.onUserScrolled.add((curOffset) => {
            this.onValueChange();
        });
        this._sBox.onScrollEnd.add(() => {
            this.onValueChange();
        });
    }

    private calculationItemRowNum(itemSize: mw.Vector2, scrollBox: mw.ScrollBox) {
        switch (scrollBox.orientation) {
            case mw.Orientation.OrientHorizontal:
                return Math.floor((scrollBox.size.x - 20) / (itemSize.x + 10)) + 2

            case mw.Orientation.OrientVertical:
                return Math.floor((scrollBox.size.y - 20) / (itemSize.y + 10)) + 2
        }
    }

    mInitCallback = new mw.Action2();
    /**调用InitData第一次初始化时的回调 */
    get InitCallback() {
        return this.mInitCallback;
    }
    mItemCallback = new mw.Action2();
    /**每个Item刷新时的回调 */
    get ItemCallback() {
        return this.mItemCallback;
    }
    setData(val) {
        this._dataArray = val;
        this.updateTotalWidth();
        this._index = -1;
        this.resetSBoxPos();
        if (this._itemArr != null) {
            for (let i = this._itemArr.length; i > 0; i--) {
                let item = this._itemArr[i - 1];
                this._itemArr.splice(i - 1, 1);
                ObjectPoolServices.GetPool(this._itemPrefab).Return(item);
                item.uiObject.visibility = (mw.SlateVisibility.Collapsed);
            }
            this.onValueChange();
        }
    }
    /**异步分帧执行 */
    async asyncSetData(val) {
        this._dataArray = val;
        this.updateTotalWidth();
        this._index = -1;
        this.resetSBoxPos();
        if (this._itemArr != null) {
            for (let i = this._itemArr.length; i > 0; i--) {
                let item = this._itemArr[i - 1];
                this._itemArr.splice(i - 1, 1);
                ObjectPoolServices.GetPool(this._itemPrefab).Return(item);
                item.uiObject.visibility = (mw.SlateVisibility.Collapsed);
                if (i % 10 == 0)
                    await TimeUtil.delaySecond(TimeUtil.deltatime())
            }
            this.onValueChange();
        }
    }
    onValueChange() {
        if (this._itemArr == null || this._dataArray == null ||
            (this._dataArray && this._dataArray.length == 0))
            return;
        let index = this.getPosIndex();
        if (index < 0 && this._index > 0) {
            index = 0;
        }
        if (this._index != index && index > -1) {
            this._index = index;
            for (let i = this._itemArr.length; i > 0; i--) {
                let item = this._itemArr[i - 1];
                if (item["scorllIndex"] < index * this._maxPerLine || (item["scorllIndex"] >= (index + this._viewLine) * this._maxPerLine)) {
                    this._itemArr.splice(i - 1, 1);
                    ObjectPoolServices.GetPool(this._itemPrefab).Return(item);
                    item.uiObject.visibility = (mw.SlateVisibility.Collapsed);
                }
            }
            for (let i = this._index * this._maxPerLine; i < (this._index + this._viewLine) * this._maxPerLine; i++) {
                if (i < 0)
                    continue;
                if (i > this._dataArray.length - 1)
                    continue;
                let isOk = false;
                for (let item of this._itemArr) {
                    if (item["scorllIndex"] == i)
                        isOk = true;
                }
                if (isOk)
                    continue;
                this.createItem(i);
            }
        }
    }
    /**
    * 根据索引号 获取当前item的位置
    * @param i   索引
    * @return 返回Pos
    */
    getPosition(i) {
        let xpos = (i % this._maxPerLine);
        let ypos = Math.floor(i / this._maxPerLine);
        switch (this._sBox.orientation) {
            case mw.Orientation.OrientHorizontal:
                return new mw.Vector2((this._cellWidth + this._spacingX) * ypos + this._leftSpace, ((this._cellHeight + this._spacingY) * xpos) + this._topSpace);
            case mw.Orientation.OrientVertical:
                return new mw.Vector2((this._cellWidth + this._spacingX) * xpos + this._leftSpace, ((this._cellHeight + this._spacingY) * ypos) + this._topSpace);
        }
    }
    destroy() {
        ObjectPoolServices.recycleAllPanel(this._itemPrefab, this._itemArr)
        ObjectPoolServices.destroyUIPool(this._itemPrefab)
    }
    getItemCount() {
        return this._maxPerLine * this._viewLine;
    }
    setItemIndex(item: T, index) {
        item["scorllIndex"] = index;
        item.uiObject.position = this.getPosition(index)
    }
    createItem(index: number) {
        let itemBase: T
        itemBase = ObjectPoolServices.createPanel(this._itemPrefab);
        (this._scrollContent.addChild(itemBase.uiObject));
        itemBase.uiObject.visibility = (mw.SlateVisibility.Visible);
        if (this._cellWidth > 0 && this._cellHeight > 0) {
            itemBase.uiObject.size = (new Vector2(this._cellWidth, this._cellHeight));
        }
        this.mInitCallback.call(index, itemBase);
        this.setItemIndex(itemBase, index);

        // if (this._dataArray && itemBase["scorllIndex"] < this._dataArray.length)
        // {
        //     this.mItemCallback.call(index, itemBase);
        // }

        this.mItemCallback.call(index, itemBase);
        this._itemArr.push(itemBase);
        if (itemBase['setOn'])
            itemBase['setOn'](this.selectIndexs.includes(index))
    }
    /**
    * 获取最上位置的索引
     * @return 返回Pos
    */
    getPosIndex() {
        let pos = this._scrollContent.position;
        switch (this._sBox.orientation) {
            case mw.Orientation.OrientHorizontal:
                {
                    return Math.floor(pos.x / -(this._cellWidth + this._spacingX));
                }
            case mw.Orientation.OrientVertical:
                {
                    let ret = pos.y / -(this._cellHeight + this._spacingY);
                    return Math.floor(ret);
                }
        }
    }
    // 这个方法的目的 就是根据总数量 行列 来计算content的真正宽度或者高度
    updateTotalWidth() {
        switch (this._sBox.orientation) {
            case mw.Orientation.OrientHorizontal:
                let width = this._cellWidth * this._dataArray.length + this._spacingX * (this._dataArray.length - 1);
                let height = this._scrollContent.size.y;
                this._scrollContent.size = (new mw.Vector2(width, height));
                break;
            case mw.Orientation.OrientVertical:
                let lineCount = Math.ceil(this._dataArray.length / this._maxPerLine);
                this._scrollContent.size = (new mw.Vector2(this._scrollContent.size.x, this._cellHeight * lineCount + this._spacingY * (lineCount - 1) + this._topSpace + 140));
                break;
        }
    }
    resetSBoxPos() {
        // 两句配合才能达到重置到顶部的效果
        this._scrollContent.position = (new mw.Vector2(0, 0));
        this._sBox.scrollToStart();
    }
    /** 选择对应索引的item
     * 
     * @param index 索引
     * @param isSingle 是否单选，默认是
     * @param isLocate 单选定位，只有单选才会定位，默认否
     */
    selectItem(index: number, isSingle: boolean = true, isLocate: boolean = false) {
        if (isSingle) {
            this.selectIndexs.length = 0
            if (isLocate)
                this.locateTo(index)
        }

        this.selectIndexs.push(index)
        for (let itemBase of this._itemArr) {
            if (itemBase['setOn']) {
                itemBase['setOn'](this.selectIndexs.includes(itemBase["scorllIndex"]))
            }
        }
    }

    /**定位到指定item */
    locateTo(index: number) {
        this._sBox.scrollOffset = this.getPosition(index).y
        setTimeout(() => {
            this.onValueChange();
        }, (TimeUtil.deltatime() + 0.001) * 2e3);
    }

    /**获取当前选中的item */
    getCurItems() {
        let curItems: T[] = []
        for (let itemBase of this._itemArr) {
            if (this.selectIndexs.includes(itemBase["scorllIndex"])) {
                curItems.push(itemBase)
            }
        }
        return curItems
    }
}