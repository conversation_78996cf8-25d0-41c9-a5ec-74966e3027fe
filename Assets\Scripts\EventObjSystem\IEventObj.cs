using SideScrollingScene;
using UnityEngine;
using static EventsTable;

namespace EventObjSystem
{

    /// <summary>
    /// 事件对象的基类
    /// </summary>
    public abstract class IEventObj : EventSpawnPoint
    {
        public static bool CheckIdValid(int eventId, EventObjType type)
        {
            EventEntry data = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventEntry(eventId);
            if (data == null)
            {
                Debug.LogError($"eventId:{eventId} eventData is null");
                return false;
            }
            return true;
            if (eventId > 1000 && eventId < 2000 && type == EventObjType.Npc)
            {
                return true;
            }
            if (eventId > 2000 && eventId < 3000 && type == EventObjType.Card)
            {
                return true;
            }
            Debug.LogError($"eventId:{eventId} is not valid for type:{type}");
            return false;
        }

        protected EventEntry eventData;
        public EventEntry EventData => eventData;
        protected string styleKey;
        public string StyleKey => styleKey;

        public virtual void Init(int eventId)
        {
            EventId = eventId;
            EventEntry data = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventEntry(eventId);
            if (!CheckIdValid(eventId, data.eventObjType))
            {
                return;
            }
            eventData = data;
            IsAutoTrigger = data.IsAutoTrigger;
            featureType = data.featureType;
            styleKey = EnumUtils.GetName(data.featureType);
            Debug.Log($"evenId:{EventId} IsAutoTrigger:{IsAutoTrigger} featureType {styleKey} EventObjType {data.eventObjType}");
        }
    }
}