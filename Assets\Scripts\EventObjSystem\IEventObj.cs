using SideScrollingScene;
using UnityEngine;
using static EventsTable;

namespace EventObjSystem
{

    /// <summary>
    /// 事件对象的基类
    /// </summary>
    public abstract class IEventObj : EventSpawnPoint
    {
        public static bool CheckIdValid(int eventId)
        {
            EventEntry data = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventEntry(eventId);
            if (data == null)
            {
                Debug.LogError($"eventId:{eventId} eventData is null");
                return false;
            }
            return true;
        }
        protected string styleKey;
        public string StyleKey => styleKey;


        public virtual void Init()
        {

        }

        public void InitByRandomId(int eventId)
        {
            EventId = eventId;
            EventEntry data = ExcelDataMgr.Instance.GetTable<EventsTable>().GetEventEntry(eventId);
            if (data == null)
            {
                Debug.LogError($"IsRandomSpawn:{IsRandomSpawn} eventId:{eventId} eventData is null");
                return;
            }
            if (!CheckIdValid(eventId))
            {
                return;
            }
            if (data.eventObjType == EventObjType.Npc)
            {
                NpcId = data.npcId;
            }
            EventObjType = data.eventObjType;
            IsAutoTrigger = data.IsAutoTrigger;
            featureType = data.featureType;
            eventCfg = data.eventCfg;
            styleKey = EnumUtils.GetName(featureType);
            Debug.Log($"evenId:{EventId} IsAutoTrigger:{IsAutoTrigger} featureType {styleKey} EventObjType {EventObjType}");
            Init();
        }


        public void InitByPoint(EventSpawnPoint point)
        {
            EventId = point.EventId;
            IsRandomSpawn = point.IsRandomSpawn;
            EventObjType = point.EventObjType;
            IsAutoTrigger = point.IsAutoTrigger;
            featureType = point.featureType;
            eventCfg = point.eventCfg;
            NpcId = point.NpcId;
            styleKey = EnumUtils.GetName(featureType);
            Debug.Log($"evenId:{EventId} IsAutoTrigger:{IsAutoTrigger} featureType {styleKey} EventObjType {EventObjType}");
            Init();
        }
    }
}