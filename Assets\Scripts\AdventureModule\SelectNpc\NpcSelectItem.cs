using UnityEngine;

namespace AdventureModule.UI
{
    public enum NpcSelectStat
    {
        NotGet,
        CantChoose,
        <PERSON><PERSON><PERSON>ose,
        <PERSON>sen
    }

    public class NpcSelectItem : NpcSelectItemGenerated
    {
        static Color NotGetColor = new Color();
        static Color CantChooseColor = Color.gray;
        static Color CanChooseColor = Color.white;
        static Color ChosenColor = Color.white;

        public void Init(int npcId)
        {

        }

        public override void OnShow()
        {

        }

        public void SetSelected(bool IsSelected)
        {

        }

        public void SetStat(NpcSelectStat stat)
        {
            switch (stat)
            {
                case NpcSelectStat.NotGet:
                    img_icon.color = NotGetColor;
                    break;
                case NpcSelectStat.CantChoose:
                    img_icon.color = CantChooseColor;
                    break;
                case NpcSelectStat.CanChoose:
                    img_icon.color = CanChooseColor;
                    break;
                case NpcSelectStat.Chosen:
                    img_icon.color = ChosenColor;
                    break;
            }
        }

        public override void OnHide()
        {

        }

        public override void OnUIReturn()
        {

        }
    }
}