using UnityEngine;
using UnityEngine.UI;

namespace AdventureModule.UI
{
    public enum NpcSelectStat
    {
        NotGet,
        CantChoose,
        CanChoose,
        Chosen
    }

    public class NpcSelectItem : NpcSelectItemGenerated, IItemRender
    {
        static Color NotGetColor = new Color();
        static Color CantChooseColor = Color.gray;
        static Color CanChooseColor = Color.white;
        static Color ChosenColor = Color.white;

        private int _scrollIndex = -1;
        public int ScrollIndex { get => _scrollIndex; set => _scrollIndex = value; }

        public GameObject UIObject => gameObject;


        public void SetData(params object[] data)
        {

        }

        public void SetSelect(bool isSelected)
        {

        }


        public void SetStat(NpcSelectStat stat)
        {
            switch (stat)
            {
                case NpcSelectStat.NotGet:
                    img_icon.color = NotGetColor;
                    break;
                case NpcSelectStat.CantChoose:
                    img_icon.color = CantChooseColor;
                    break;
                case NpcSelectStat.CanChoose:
                    img_icon.color = CanChooseColor;
                    break;
                case NpcSelectStat.Chosen:
                    img_icon.color = ChosenColor;
                    break;
            }
        }

        public override void OnShow()
        {

        }

        public override void OnHide()
        {

        }

        public override void OnUIReturn()
        {

        }

        public void Destroy()
        {

        }
    }
}