
namespace AdventureModule
{
    public class AdventurePanel : AdventurePanelGenerated
    {

        public override void OnShow()
        {
            tm_mapItemName.text = "";
        }

        public void SetName(MapItem item)
        {
            if (item != null)
            {
                img_symbol_1.gameObject.SetActive(true);
                img_symbol_2.gameObject.SetActive(true);
                tm_mapItemName.text = item.sceneName;
            }
            else
            {
                tm_mapItemName.text = "";
                img_symbol_1.gameObject.SetActive(false);
                img_symbol_2.gameObject.SetActive(false);
            }
        }

        public override void OnHide()
        {

        }

        public override void OnUIReturn()
        {

        }
    }
}