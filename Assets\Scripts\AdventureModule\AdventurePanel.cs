
namespace AdventureModule
{
    public class AdventurePanel : AdventurePanelGenerated
    {
        // JavaScript风格的多参数重载
        public virtual void OnShow(int adventureId, string adventureName)
        {
            tm_mapItemName.text = adventureName;
            // 其他UI初始化逻辑...
        }

        // 更多参数的重载
        public virtual void OnShow(int adventureId, string adventureName, string description)
        {
            tm_mapItemName.text = adventureName;
            // 可以处理描述信息...
        }

        // 单参数重载
        public virtual void OnShow(string adventureName)
        {
            tm_mapItemName.text = adventureName;
        }

        // 无参版本 - 兼容旧代码
        public override void OnShow()
        {
            tm_mapItemName.text = "默认冒险";
        }

        public override void OnHide()
        {

        }

        public override void OnUIReturn()
        {

        }
    }
}