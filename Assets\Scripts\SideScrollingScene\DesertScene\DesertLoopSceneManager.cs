using UnityEngine;

namespace SideScrollingScene
{

    /// <summary>
    /// 针对沙漠场景做的循环定制
    /// </summary>
    public class DesertLoopSceneManager : ChunkStreamingManager
    {
        private static DesertLoopSceneManager _instance;
        public static DesertLoopSceneManager Instance
        {
            get
            {
                return _instance;
            }
        }

        public static Vector3 RightSideAnchor = new();
        public static Vector3 LeftSideAnchor = new(-131.9f, 0, 0);

        public GameObject LoopArea;
        bool _bIsTowardsRightSide = false;
        bool _bIsInLoopArea = false;

        protected override void Awake()
        {
            base.Awake();
            _instance = this;
        }


        override protected void OnUpdate()
        {
            return;
            if (_bIsInLoopArea)
            {
                Switch();
            }
        }

        public void SwitchForceByNormalArea(bool IsEnterNormalArea)
        {
            LoopArea.transform.position = IsEnterNormalArea ? LeftSideAnchor : RightSideAnchor;
            _bIsTowardsRightSide = IsEnterNormalArea;

            //情况一 从固定区域左侧出来 
            if (!IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Left)
            {
                LoopArea.transform.position = LeftSideAnchor;
                _bIsInLoopArea = true;
                _bIsTowardsRightSide = false;
            }

            //情况二 从固定区域右侧出来
            if (!IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Right)
            {
                LoopArea.transform.position = RightSideAnchor;
                _bIsInLoopArea = true;
                _bIsTowardsRightSide = true;
            }

            //情况三 从固定区域左侧进入
            if (IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Right)
            {
                LoopArea.transform.position = LeftSideAnchor;
                _bIsInLoopArea = false;
                _bIsTowardsRightSide = true;
            }

            //情况四 从固定区域右侧进入
            if (IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Left)
            {
                LoopArea.transform.position = RightSideAnchor;
                _bIsInLoopArea = false;
                _bIsTowardsRightSide = false;
            }
        }

        public void SwitchForceByLeaveLoopArea()
        {

            //情况一 从循环检测区域左侧出来
            if (player.PlayerFacingDirection == E_PlayerFacingDirection.Left)
            {
                player.transform.parent = LoopArea.transform;
                Camera.main.transform.parent = player.transform;
                LoopArea.transform.position = RightSideAnchor;
                // player.SetPosition(player.transform.position);
                player.transform.parent = null;
                Camera.main.transform.parent = null;
            }

            //情况二 从循环检测区域右侧出来 
            if (player.PlayerFacingDirection == E_PlayerFacingDirection.Right)
            {
                player.transform.parent = LoopArea.transform;
                Camera.main.transform.parent = player.transform;
                LoopArea.transform.position = LeftSideAnchor;
                // player.SetPosition(player.transform.position);
                player.transform.parent = null;
                Camera.main.transform.parent = null;
            }
        }



        void Switch()
        {

            if (player)
            {
                SwitchInternal(player.PlayerFacingDirection == E_PlayerFacingDirection.Right);
            }

        }

        void SwitchInternal(bool TowardsRightSide)
        {
            if (_bIsTowardsRightSide == TowardsRightSide)
            {
                return;
            }

            player.Moveable = false;
            LoopArea.transform.position = TowardsRightSide ? LeftSideAnchor : RightSideAnchor;
            _bIsTowardsRightSide = TowardsRightSide;
            player.Moveable = true;
        }


    }
}