using UnityEngine;

namespace SideScrollingScene
{

    /// <summary>
    /// 针对沙漠场景做的循环定制
    /// </summary>
    public class DesertLoopSceneManager : ChunkStreamingManager
    {
        private static DesertLoopSceneManager _instance;

        public static DesertLoopSceneManager Instance
        {
            get
            {
                return _instance;
            }
        }

        protected override void Awake()
        {
            base.Awake();
            _instance = this;
        }

        public static Vector3 OriginalAnchor = new();
        public static Vector3 LoopAnchor = new(-131.9f, 0, 0);
        private bool IsLooping;

        public LoopTrigger loopTrigger;

        public void Switch()
        {

        }

        void OnDrawGizmos()
        {
            // 只在物体激活且初始化完成时绘制Gizmos
            if (!gameObject.activeInHierarchy)
                return;

            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(LoopAnchor, Camera.main.transform.lossyScale);
        }
    }
}