using UnityEngine;

namespace SideScrollingScene
{

    /// <summary>
    /// 针对沙漠场景做的循环定制
    /// </summary>
    public class DesertLoopSceneManager : ChunkStreamingManager
    {
        private static DesertLoopSceneManager _instance;
        public static DesertLoopSceneManager Instance
        {
            get
            {
                return _instance;
            }
        }

        protected override void Awake()
        {
            base.Awake();
            _instance = this;
        }

        public static Vector3 OriginalAnchor = new();
        public static Vector3 LoopAnchor = new(-131.9f, 0, 0);

        public GameObject LoopArea;

        bool IsLeft = false;
        void Update()
        {
            Switch();
        }

        public void Switch()
        {
            if (!IsLeft && player && player.PlayerFacingDirection == E_PlayerFacingDirection.Left)
            {
                LoopArea.transform.position = LoopAnchor;
                IsLeft = true;
            }

            if (IsLeft && player && player.PlayerFacingDirection == E_PlayerFacingDirection.Right)
            {
                LoopArea.transform.position = OriginalAnchor;
                IsLeft = false;
            }
        }


    }
}