using UnityEngine;

namespace SideScrollingScene
{

    /// <summary>
    /// 针对沙漠场景做的循环定制
    /// </summary>
    public class DesertLoopSceneManager : ChunkStreamingManager
    {
        private static DesertLoopSceneManager _instance;
        public static DesertLoopSceneManager Instance
        {
            get
            {
                return _instance;
            }
        }

        public static Vector3 RightSideAnchor = new();
        public static Vector3 LeftSideAnchor = new(-131.9f, 0, 0);

        public GameObject LoopArea;

        public GameObject NormalAreaTrigger;

        protected override void Awake()
        {
            base.Awake();
            _instance = this;
        }

        protected override void OnAfterPlayerInitBeforePlayerActive()
        {
            if (player.transform.position.x < NormalAreaTrigger.transform.position.x)
            {
                LoopArea.transform.position = LeftSideAnchor;
            }
            else
            {
                LoopArea.transform.position = RightSideAnchor;
            }
            CheckChunks();
        }

        public void SwitchForceByNormalArea(bool IsEnterNormalArea)
        {
            LoopArea.transform.position = IsEnterNormalArea ? LeftSideAnchor : RightSideAnchor;

            //情况一 从固定区域左侧出来 
            if (!IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Left)
            {
                LoopArea.transform.position = LeftSideAnchor;
            }

            //情况二 从固定区域右侧出来
            if (!IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Right)
            {
                LoopArea.transform.position = RightSideAnchor;
            }

            //情况三 从固定区域左侧进入
            if (IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Right)
            {
                LoopArea.transform.position = LeftSideAnchor;
            }

            //情况四 从固定区域右侧进入
            if (IsEnterNormalArea && player.PlayerFacingDirection == E_PlayerFacingDirection.Left)
            {
                LoopArea.transform.position = RightSideAnchor;
            }
        }

        public void SwitchForceByLeaveLoopArea()
        {
            //情况一 从循环检测区域左侧出来
            Vector3 pos = RightSideAnchor;
            //情况二 从循环检测区域右侧出来 
            if (player.PlayerFacingDirection == E_PlayerFacingDirection.Right)
            {
                pos = LeftSideAnchor;
            }

            player.transform.parent = LoopArea.transform;
            Camera.main.transform.parent = player.transform;
            LoopArea.transform.position = pos;
            player.transform.parent = null;
            Camera.main.transform.parent = null;
        }

    }
}