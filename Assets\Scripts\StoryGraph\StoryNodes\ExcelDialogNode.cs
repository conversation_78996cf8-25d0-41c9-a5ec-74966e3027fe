using System.Collections.Generic;
using Commmon;
using Common;
using Story;
using StoryCommon;
using UnityEngine;

namespace StoryEditor
{
    public class ExcelDialogNode : BaseNode
    {
        [Input] public Empty input;
        [Output] public Empty output;

        public int excelDialogID;
        private int _curDialogIndex = 0;
        private List<DiagueData> _curDialogDatas = null;
        protected override void OnStart()
        {
            base.OnStart();
            var storyGraph = graph as StoryGraph;
            storyGraph.curDialogNode = this;
            _curDialogIndex = 0;
            // DialogDatas allExcelDialogs = ResourceMgr.Instance.LoadResource<DialogDatas>("datas/DialogDatas");
            // allExcelDialogs.Translate = Translate;
            ExcelDataMgr.Instance.SetTranslationFunc<DialogDatas, DiagueData>("content");
            DialogDatas allExcelDialogs = ExcelDataMgr.Instance.GetTable<DialogDatas>();
            _curDialogDatas = allExcelDialogs.GetDiagueDataList(excelDialogID);
            CheckContinueOrFinish();
        }

        private void CheckContinueOrFinish()
        {
            if (_curDialogIndex >= _curDialogDatas.Count)
            {
                var storyGraph = graph as StoryGraph;
                if (storyGraph != null) storyGraph.FinishNode(this, "output");
            }
            else
            {
                int oldIndex = _curDialogIndex;
                _curDialogIndex += 1;
                ShowDialogByIndex(oldIndex);
            }

        }

        private void ShowDialogByIndex(int index)
        {
            var dialogData = _curDialogDatas[index];

            var dialogInfo = new DialogInfo();
            dialogInfo.characterID = dialogData.charcterID;
            dialogInfo.content = dialogData.content;
            dialogInfo.animName = dialogData.animName;
            dialogInfo.animLoop = dialogData.animLoop;
            StoryManager.Instance.ShowDialog(dialogInfo, CheckContinueOrFinish);
        }

        protected override void OnFinish()
        {
            base.OnFinish();
            for (int index = _curDialogIndex; index < _curDialogDatas.Count; index++)
            {
                ShowDialogByIndex(index);
            }
            StoryManager.Instance.SkipDialogToComplete();
        }
    }
}